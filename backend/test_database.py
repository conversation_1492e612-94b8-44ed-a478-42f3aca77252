#!/usr/bin/env python3
"""
数据库功能测试脚本
"""
import os
import sys
import logging
from dotenv import load_dotenv, find_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv(find_dotenv())

from backend.app.helpers.database_helper import (
    Database,
    DatabaseError,
    execute,
    fetch_one,
    fetch_all,
    execute_returning,
    execute_many,
    fetch_dict_one,
    fetch_dict_all,
    count,
    exists,
    execute_script,
    check_connection,
    transaction,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


def test_basic_connection():
    """测试基本连接"""
    logger.info("测试数据库连接...")
    try:
        result = check_connection()
        if result:
            logger.info("✅ 数据库连接正常")
            return True
        else:
            logger.error("❌ 数据库连接失败")
            return False
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False


def test_basic_queries():
    """测试基本查询功能"""
    logger.info("测试基本查询功能...")
    try:
        # 测试简单查询
        result = fetch_one("SELECT 1 as test_value")
        if result and result[0] == 1:
            logger.info("✅ 基本查询功能正常")
        else:
            logger.error("❌ 基本查询功能异常")
            return False

        # 测试字典格式查询
        dict_result = fetch_dict_one("SELECT 1 as test_value, 'hello' as test_text")
        if (
            dict_result
            and dict_result["test_value"] == 1
            and dict_result["test_text"] == "hello"
        ):
            logger.info("✅ 字典格式查询功能正常")
        else:
            logger.error("❌ 字典格式查询功能异常")
            return False

        return True
    except Exception as e:
        logger.error(f"❌ 基本查询测试失败: {e}")
        return False


def test_configuration_table():
    """测试配置表操作"""
    logger.info("测试配置表操作...")
    try:
        # 检查配置表是否存在
        table_exists = exists(
            "information_schema.tables",
            "table_name = %s AND table_schema = 'public'",
            ("configuration",),
        )

        if table_exists:
            logger.info("✅ 配置表存在")

            # 测试统计功能
            config_count = count("configuration")
            logger.info(f"✅ 配置表记录数量: {config_count}")

            # 测试查询所有配置
            configs = fetch_dict_all("SELECT key, value FROM configuration LIMIT 5")
            logger.info(f"✅ 查询到配置数量: {len(configs)}")
            for config in configs:
                logger.info(f"   配置: {config['key']} = {config['value']}")

        else:
            logger.warning("⚠️ 配置表不存在，可能需要运行数据库迁移")

        return True
    except Exception as e:
        logger.error(f"❌ 配置表操作测试失败: {e}")
        return False


def test_transaction():
    """测试事务功能"""
    logger.info("测试事务功能...")
    try:
        # 使用配置表来测试事务功能，避免临时表的作用域问题
        # 首先检查是否有测试配置
        test_key = "TEST_TRANSACTION_KEY"

        # 清理可能存在的测试数据
        execute("DELETE FROM configuration WHERE key = %s", (test_key,))

        # 测试事务回滚
        try:
            with transaction() as conn:
                Database.execute_with_conn(
                    conn,
                    "INSERT INTO configuration (key, value) VALUES (%s, %s)",
                    (test_key, "test_value"),
                )
                # 故意引发错误来测试回滚
                Database.execute_with_conn(
                    conn,
                    "INSERT INTO configuration (key, value) VALUES (%s, %s, %s)",
                    (test_key, "test_value", "extra"),
                )  # 这会失败
        except DatabaseError:
            logger.info("✅ 事务回滚测试正常（预期的错误）")

        # 检查数据是否被回滚
        test_exists = exists("configuration", "key = %s", (test_key,))
        if not test_exists:
            logger.info("✅ 事务回滚功能正常")
        else:
            logger.error("❌ 事务回滚功能异常，测试数据仍然存在")
            return False

        # 测试正常事务提交
        with transaction() as conn:
            Database.execute_with_conn(
                conn,
                "INSERT INTO configuration (key, value) VALUES (%s, %s)",
                (test_key, "committed_value"),
            )

        # 检查数据是否被提交
        test_exists = exists("configuration", "key = %s", (test_key,))
        if test_exists:
            logger.info("✅ 事务提交功能正常")
            # 清理测试数据
            execute("DELETE FROM configuration WHERE key = %s", (test_key,))
        else:
            logger.error("❌ 事务提交功能异常，测试数据未被提交")
            return False

        return True
    except Exception as e:
        logger.error(f"❌ 事务功能测试失败: {e}")
        return False


def test_batch_operations():
    """测试批量操作功能"""
    logger.info("测试批量操作功能...")
    try:
        # 创建临时测试表
        create_table_sql = """
        CREATE TEMPORARY TABLE test_batch (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100),
            value INTEGER
        )
        """
        execute(create_table_sql)
        logger.info("✅ 临时测试表创建成功")

        # 测试批量插入
        batch_data = [
            ("item1", 10),
            ("item2", 20),
            ("item3", 30),
            ("item4", 40),
            ("item5", 50),
        ]

        affected_rows = execute_many(
            "INSERT INTO test_batch (name, value) VALUES (%s, %s)", batch_data
        )

        if affected_rows == 5:
            logger.info("✅ 批量插入功能正常")
        else:
            logger.error(f"❌ 批量插入功能异常，影响行数: {affected_rows}，期望5行")
            return False

        # 验证数据
        batch_count = count("test_batch")
        if batch_count == 5:
            logger.info("✅ 批量插入数据验证正常")
        else:
            logger.error(f"❌ 批量插入数据验证异常，记录数: {batch_count}，期望5条")
            return False

        return True
    except Exception as e:
        logger.error(f"❌ 批量操作测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始数据库功能测试...")

    tests = [
        ("数据库连接", test_basic_connection),
        ("基本查询", test_basic_queries),
        ("配置表操作", test_configuration_table),
        ("事务功能", test_transaction),
        ("批量操作", test_batch_operations),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")

    logger.info(f"\n🎯 测试结果: {passed}/{total} 通过")

    if passed == total:
        logger.info("🎉 所有数据库功能测试通过！")
        return True
    else:
        logger.error("💥 部分数据库功能测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
