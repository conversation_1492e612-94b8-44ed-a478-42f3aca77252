-- 初始化数据库脚本
-- 创建数据库和用户

-- 创建数据库（如果不存在）
-- 注意：这个命令需要在PostgreSQL命令行中以超级用户身份执行
-- CREATE DATABASE petkit_translator;

-- 创建用户（如果不存在）
-- CREATE USER petkit_user WITH PASSWORD 'petkit_password';

-- 授权
-- GRANT ALL PRIVILEGES ON DATABASE petkit_translator TO petkit_user;

-- 连接到数据库后执行以下命令
-- \c petkit_translator;

-- 授权用户在数据库中的权限
-- GRANT ALL ON SCHEMA public TO petkit_user;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO petkit_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO petkit_user;

-- 设置默认权限
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO petkit_user;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO petkit_user;
