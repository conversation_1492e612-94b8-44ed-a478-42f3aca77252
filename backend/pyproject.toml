[project]
name = "translator-backend"
version = "0.1.0"
description = "简单的Flask翻译服务"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
    "flask>=3.0.0",
    "flask-cors>=4.0.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
    "mysql-connector-python>=8.0.0",
    "langchain>=0.1.0",
    "langchain-community>=0.0.20",
    "dashscope>=1.14.0",
    "openpyxl>=3.0.0",
    "pandas>=2.0.0",
    "psycopg2-binary>=2.9.10",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[tool.black]
line-length = 88
target-version = ['py311']

[[tool.uv.index]]
url = "https://mirror.nyist.edu.cn/pypi/web/simple"
default = true


