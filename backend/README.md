# 翻译服务后端

基于Flask的简单翻译服务，采用分层架构设计。

## 项目结构

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # 应用入口
│   ├── api/                 # API接口层
│   │   ├── __init__.py
│   │   └── translate_api.py # 翻译接口
│   ├── services/            # 服务层
│   │   ├── __init__.py
│   │   └── translate_service.py # 翻译服务
│   ├── models/              # 数据模型层
│   │   ├── __init__.py
│   │   └── dto.py           # 数据传输对象
│   └── config/              # 配置层
│       ├── __init__.py
│       └── settings.py      # 应用配置
├── .env.example             # 环境变量示例
├── pyproject.toml           # 项目配置
├── run.py                   # 启动脚本
└── README.md                # 项目说明
```

## 分层架构说明

1. **API层** (`app/api/`): 处理HTTP请求和响应，参数验证
2. **服务层** (`app/services/`): 业务逻辑处理
3. **模型层** (`app/models/`): 数据传输对象定义
4. **配置层** (`app/config/`): 应用配置管理

## 安装和运行

1. 安装依赖：
```bash
uv sync
```

2. 复制环境变量文件：
```bash
cp .env.example .env
```

3. 启动服务：
```bash
python run.py
```
