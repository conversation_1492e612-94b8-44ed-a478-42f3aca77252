# PostgreSQL 数据库设置指南

## 1. 安装 PostgreSQL

### macOS

```bash
# 使用Homebrew安装
brew install postgresql
brew services start postgresql
```

### Ubuntu/Debian

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Windows

下载并安装 PostgreSQL 官方安装包：https://www.postgresql.org/download/windows/

## 2. 创建数据库和用户

```bash
# 连接到PostgreSQL
sudo -u postgres psql

# 或者在macOS上
psql postgres
```

在 PostgreSQL 命令行中执行：

```sql
-- 创建数据库
CREATE DATABASE petkit_translator;

-- 创建用户
CREATE USER petkit WITH PASSWORD 'petkit123';

-- 授权
GRANT ALL PRIVILEGES ON DATABASE petkit_translator TO petkit;

-- 连接到新数据库
\c petkit_translator;

-- 授权用户在数据库中的权限
GRANT ALL ON SCHEMA public TO petkit;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO petkit;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO petkit;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO petkit;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO petkit;

-- 退出
\q
```

## 3. 配置环境变量

复制 `.env.example` 到 `.env` 并配置 PostgreSQL 连接信息：

```env
# PostgreSQL数据库配置
PG_HOST=localhost
PG_PORT=5432
PG_USER=petkit
PG_PASSWORD=petkit123
PG_DATABASE=petkit_translator
PG_POOL_SIZE=10
```

## 4. 安装 Python 依赖

```bash
# 安装psycopg2（PostgreSQL适配器）
pip install psycopg2-binary

# 或者如果上面的命令失败，尝试：
pip install psycopg2
```

## 5. 运行数据库迁移

```bash
# 检查数据库连接
python scripts/migrate.py check

# 运行迁移
python scripts/migrate.py migrate
```

## 6. 验证设置

迁移成功后，configuration 表将包含以下默认配置：

| key              | value    | 说明                              |
| ---------------- | -------- | --------------------------------- |
| QWEN_API_KEY     | NULL     | 通义千问 API 密钥（需要手动设置） |
| QWEN_MODEL_NAME  | qwen-max | 模型名称                          |
| QWEN_TEMPERATURE | 0.7      | 温度参数                          |
| QWEN_MAX_TOKENS  | 2000     | 最大 token 数                     |

## 7. 设置 API 密钥

通过 API 设置 QWEN_API_KEY：

```bash
# 设置API密钥
curl -X PUT http://localhost:32982/api/config/QWEN_API_KEY/value \
  -H "Content-Type: application/json" \
  -d '{"value": "your_actual_api_key_here"}'

# 验证设置
curl http://localhost:32982/api/config/QWEN_API_KEY
```

## 8. 配置管理 API

系统提供了完整的配置管理 API：

- `GET /api/config` - 获取所有配置
- `GET /api/config/{key}` - 获取指定配置
- `POST /api/config` - 创建新配置
- `PUT /api/config/{key}` - 更新配置
- `DELETE /api/config/{key}` - 删除配置
- `GET /api/config/{key}/value` - 获取配置值
- `PUT /api/config/{key}/value` - 设置配置值

## 故障排除

### 连接失败

1. 检查 PostgreSQL 服务是否运行
2. 验证用户名、密码和数据库名称
3. 确认防火墙设置允许连接

### 权限错误

确保用户有足够的权限：

```sql
GRANT ALL PRIVILEGES ON DATABASE petkit_translator TO petkit_user;
```

### 迁移失败

检查 SQL 语法和数据库连接，查看日志获取详细错误信息。
