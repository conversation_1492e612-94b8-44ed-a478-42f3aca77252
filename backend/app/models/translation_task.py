"""
翻译任务模型
"""
import json
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class TranslationConfig:
    """翻译配置"""
    style: str = "formal"  # formal, informal, casual
    domain: str = "general"  # general, technical, business
    preserve_formatting: bool = True

    def to_dict(self) -> Dict[str, Any]:
        return {
            'style': self.style,
            'domain': self.domain,
            'preserve_formatting': self.preserve_formatting
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TranslationConfig':
        return cls(
            style=data.get('style', 'formal'),
            domain=data.get('domain', 'general'),
            preserve_formatting=data.get('preserve_formatting', True)
        )


@dataclass
class TranslationTask:
    """翻译任务模型"""
    id: str
    status: str = "processing"  # processing, completed, failed
    total_records: int = 0
    completed_records: int = 0
    failed_records: int = 0
    config: Optional[TranslationConfig] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def create(cls, task_id: str, total_records: int, config: Dict[str, Any], db_connection) -> 'TranslationTask':
        """创建翻译任务"""
        sql = """
        INSERT INTO translation_tasks (id, total_records, config, status, created_at, updated_at)
        VALUES (%s, %s, %s, 'processing', NOW(), NOW())
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id, total_records, json.dumps(config)))
            db_connection.commit()

            logger.info(f"翻译任务创建成功: {task_id}")

            # 返回创建的任务对象
            return cls.get_by_id(task_id, db_connection)

        except Exception as e:
            db_connection.rollback()
            logger.error(f"创建翻译任务失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_by_id(cls, task_id: str, db_connection) -> Optional['TranslationTask']:
        """根据ID获取翻译任务"""
        sql = """
        SELECT id, status, total_records, completed_records, failed_records,
               config, created_at, updated_at
        FROM translation_tasks WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()

            if result:
                config_data = json.loads(result[5]) if result[5] else {}
                return cls(
                    id=result[0],
                    status=result[1],
                    total_records=result[2],
                    completed_records=result[3],
                    failed_records=result[4],
                    config=TranslationConfig.from_dict(config_data),
                    created_at=result[6],
                    updated_at=result[7]
                )
            return None

        except Exception as e:
            logger.error(f"获取翻译任务失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_all(cls, db_connection, limit: int = 50, offset: int = 0) -> List['TranslationTask']:
        """获取所有翻译任务"""
        sql = """
        SELECT id, status, total_records, completed_records, failed_records,
               config, created_at, updated_at
        FROM translation_tasks
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (limit, offset))
            results = cursor.fetchall()

            tasks = []
            for result in results:
                config_data = json.loads(result[5]) if result[5] else {}
                tasks.append(cls(
                    id=result[0],
                    status=result[1],
                    total_records=result[2],
                    completed_records=result[3],
                    failed_records=result[4],
                    config=TranslationConfig.from_dict(config_data),
                    created_at=result[6],
                    updated_at=result[7]
                ))

            return tasks

        except Exception as e:
            logger.error(f"获取翻译任务列表失败: {e}")
            raise
        finally:
            cursor.close()

    def update_status(self, status: str, db_connection):
        """更新任务状态"""
        sql = """
        UPDATE translation_tasks
        SET status = %s, updated_at = NOW()
        WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (status, self.id))
            db_connection.commit()
            self.status = status

            logger.info(f"任务状态更新成功: {self.id} -> {status}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"更新任务状态失败: {e}")
            raise
        finally:
            cursor.close()

    def update_progress(self, completed_records: int, failed_records: int, db_connection):
        """更新任务进度"""
        sql = """
        UPDATE translation_tasks
        SET completed_records = %s, failed_records = %s, updated_at = NOW()
        WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (completed_records, failed_records, self.id))
            db_connection.commit()

            self.completed_records = completed_records
            self.failed_records = failed_records

            logger.info(f"任务进度更新成功: {self.id} - 完成: {completed_records}, 失败: {failed_records}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"更新任务进度失败: {e}")
            raise
        finally:
            cursor.close()

    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.total_records == 0:
            return 0.0

        processed_records = self.completed_records + self.failed_records
        return (processed_records / self.total_records) * 100

    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return (self.completed_records + self.failed_records) >= self.total_records

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'status': self.status,
            'total_records': self.total_records,
            'completed_records': self.completed_records,
            'failed_records': self.failed_records,
            'config': self.config.to_dict() if self.config else {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'progress_percentage': self.get_progress_percentage()
        }

    @classmethod
    def get_by_status(cls, status: str, db_connection, limit: int = 50) -> List['TranslationTask']:
        """根据状态获取翻译任务"""
        sql = """
        SELECT id, status, total_records, completed_records, failed_records,
               config, created_at, updated_at
        FROM translation_tasks
        WHERE status = %s
        ORDER BY created_at DESC
        LIMIT %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (status, limit))
            results = cursor.fetchall()

            tasks = []
            for result in results:
                config_data = json.loads(result[5]) if result[5] else {}
                tasks.append(cls(
                    id=result[0],
                    status=result[1],
                    total_records=result[2],
                    completed_records=result[3],
                    failed_records=result[4],
                    config=TranslationConfig.from_dict(config_data),
                    created_at=result[6],
                    updated_at=result[7]
                ))

            return tasks

        except Exception as e:
            logger.error(f"根据状态获取翻译任务失败: {e}")
            raise
        finally:
            cursor.close()

    def delete(self, db_connection):
        """删除翻译任务（级联删除相关记录）"""
        sql = "DELETE FROM translation_tasks WHERE id = %s"

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (self.id,))
            db_connection.commit()

            logger.info(f"翻译任务删除成功: {self.id}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"删除翻译任务失败: {e}")
            raise
        finally:
            cursor.close()