"""
翻译记录模型
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class TranslationItem:
    """翻译项目"""
    id: int
    text: str
    target_languages: List[str]


@dataclass
class TranslationRecord:
    """翻译记录模型"""
    id: Optional[int]
    task_id: str
    source_id: int
    source_text: str
    target_language: str
    translated_text: Optional[str] = None
    status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None

    @classmethod
    def create_batch(cls, task_id: str, translation_items: List[TranslationItem], db_connection) -> int:
        """批量创建翻译记录"""
        sql = """
        INSERT INTO translation_records (task_id, source_id, source_text, target_language, status)
        VALUES (%s, %s, %s, %s, 'pending')
        """

        cursor = db_connection.cursor()
        try:
            records = []
            for item in translation_items:
                for target_lang in item.target_languages:
                    records.append((task_id, item.id, item.text, target_lang))

            cursor.executemany(sql, records)
            db_connection.commit()

            record_count = len(records)
            logger.info(f"批量创建翻译记录成功: {record_count} 条记录")

            return record_count

        except Exception as e:
            db_connection.rollback()
            logger.error(f"批量创建翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_by_task_id(cls, task_id: str, db_connection) -> List['TranslationRecord']:
        """根据任务ID获取所有翻译记录"""
        sql = """
        SELECT id, task_id, source_id, source_text, target_language,
               translated_text, status, error_message, created_at
        FROM translation_records
        WHERE task_id = %s
        ORDER BY source_id, target_language
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id,))
            results = cursor.fetchall()

            records = []
            for result in results:
                records.append(cls(
                    id=result[0],
                    task_id=result[1],
                    source_id=result[2],
                    source_text=result[3],
                    target_language=result[4],
                    translated_text=result[5],
                    status=result[6],
                    error_message=result[7],
                    created_at=result[8]
                ))

            return records

        except Exception as e:
            logger.error(f"根据任务ID获取翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_pending_records(cls, task_id: str, db_connection) -> List['TranslationRecord']:
        """获取待翻译的记录"""
        sql = """
        SELECT id, task_id, source_id, source_text, target_language,
               translated_text, status, error_message, created_at
        FROM translation_records
        WHERE task_id = %s AND status = 'pending'
        ORDER BY source_id, target_language
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id,))
            results = cursor.fetchall()

            records = []
            for result in results:
                records.append(cls(
                    id=result[0],
                    task_id=result[1],
                    source_id=result[2],
                    source_text=result[3],
                    target_language=result[4],
                    translated_text=result[5],
                    status=result[6],
                    error_message=result[7],
                    created_at=result[8]
                ))

            return records

        except Exception as e:
            logger.error(f"获取待翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    def update_translation(self, translated_text: str, status: str, db_connection, error_message: Optional[str] = None):
        """更新翻译结果"""
        sql = """
        UPDATE translation_records
        SET translated_text = %s, status = %s, error_message = %s
        WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (translated_text, status, error_message, self.id))
            db_connection.commit()

            self.translated_text = translated_text
            self.status = status
            self.error_message = error_message

            logger.info(f"翻译记录更新成功: {self.id} -> {status}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"更新翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    def update_status(self, status: str, db_connection, error_message: Optional[str] = None):
        """更新记录状态"""
        sql = """
        UPDATE translation_records
        SET status = %s, error_message = %s
        WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (status, error_message, self.id))
            db_connection.commit()

            self.status = status
            self.error_message = error_message

            logger.info(f"记录状态更新成功: {self.id} -> {status}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"更新记录状态失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_task_statistics(cls, task_id: str, db_connection) -> Dict[str, int]:
        """获取任务统计信息"""
        sql = """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing
        FROM translation_records
        WHERE task_id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()

            return {
                'total': result[0] or 0,
                'completed': result[1] or 0,
                'failed': result[2] or 0,
                'pending': result[3] or 0,
                'processing': result[4] or 0
            }

        except Exception as e:
            logger.error(f"获取任务统计信息失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def get_by_id(cls, record_id: int, db_connection) -> Optional['TranslationRecord']:
        """根据ID获取翻译记录"""
        sql = """
        SELECT id, task_id, source_id, source_text, target_language,
               translated_text, status, error_message, created_at
        FROM translation_records
        WHERE id = %s
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (record_id,))
            result = cursor.fetchone()

            if result:
                return cls(
                    id=result[0],
                    task_id=result[1],
                    source_id=result[2],
                    source_text=result[3],
                    target_language=result[4],
                    translated_text=result[5],
                    status=result[6],
                    error_message=result[7],
                    created_at=result[8]
                )
            return None

        except Exception as e:
            logger.error(f"根据ID获取翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'source_id': self.source_id,
            'source_text': self.source_text,
            'target_language': self.target_language,
            'translated_text': self.translated_text,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def get_by_status(cls, task_id: str, status: str, db_connection) -> List['TranslationRecord']:
        """根据任务ID和状态获取翻译记录"""
        sql = """
        SELECT id, task_id, source_id, source_text, target_language,
               translated_text, status, error_message, created_at
        FROM translation_records
        WHERE task_id = %s AND status = %s
        ORDER BY source_id, target_language
        """

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id, status))
            results = cursor.fetchall()

            records = []
            for result in results:
                records.append(cls(
                    id=result[0],
                    task_id=result[1],
                    source_id=result[2],
                    source_text=result[3],
                    target_language=result[4],
                    translated_text=result[5],
                    status=result[6],
                    error_message=result[7],
                    created_at=result[8]
                ))

            return records

        except Exception as e:
            logger.error(f"根据状态获取翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    def delete(self, db_connection):
        """删除翻译记录"""
        sql = "DELETE FROM translation_records WHERE id = %s"

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (self.id,))
            db_connection.commit()

            logger.info(f"翻译记录删除成功: {self.id}")

        except Exception as e:
            db_connection.rollback()
            logger.error(f"删除翻译记录失败: {e}")
            raise
        finally:
            cursor.close()

    @classmethod
    def delete_by_task_id(cls, task_id: str, db_connection):
        """根据任务ID删除所有相关翻译记录"""
        sql = "DELETE FROM translation_records WHERE task_id = %s"

        cursor = db_connection.cursor()
        try:
            cursor.execute(sql, (task_id,))
            deleted_count = cursor.rowcount
            db_connection.commit()

            logger.info(f"任务相关翻译记录删除成功: {task_id}, 删除数量: {deleted_count}")

            return deleted_count

        except Exception as e:
            db_connection.rollback()
            logger.error(f"删除任务相关翻译记录失败: {e}")
            raise
        finally:
            cursor.close()