"""
统一API响应模型
"""
from typing import Any, Optional, Union, Dict, List
from pydantic import BaseModel, Field


class ApiResponse(BaseModel):
    """
    统一API响应格式

    用于标准化所有API接口的响应格式，确保前后端数据交互的一致性
    """
    code: int = Field(..., description="响应状态码，0表示成功，负数表示失败")
    message: str = Field(..., description="响应消息")
    data: Optional[Union[Dict[str, Any], List[Any], str, int, bool, Any]] = Field(
        None,
        description="响应数据，可以是字典、列表、基本类型或任意数据类型"
    )

    @classmethod
    def success(cls, data: Optional[Any] = None, message: str = "操作成功") -> "ApiResponse":
        """
        创建成功响应

        Args:
            data: 响应数据
            message: 成功消息

        Returns:
            ApiResponse: 成功响应实例
        """
        return cls(code=0, message=message, data=data)

    @classmethod
    def error(cls, code: int = -1, message: str = "操作失败", data: Optional[Any] = None) -> "ApiResponse":
        """
        创建错误响应

        Args:
            code: 错误码，默认为-1
            message: 错误消息
            data: 错误详情数据

        Returns:
            ApiResponse: 错误响应实例
        """
        return cls(code=code, message=message, data=data)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 字典格式的响应数据
        """
        result = {
            "code": self.code,
            "message": self.message
        }
        if self.data is not None:
            result["data"] = self.data
        return result

    def is_success(self) -> bool:
        """
        判断是否为成功响应

        Returns:
            bool: True表示成功，False表示失败
        """
        return self.code == 0

    def is_error(self) -> bool:
        """
        判断是否为错误响应

        Returns:
            bool: True表示错误，False表示成功
        """
        return self.code != 0