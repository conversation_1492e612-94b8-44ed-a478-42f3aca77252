"""
翻译相关数据模型定义
"""

from typing import Optional, Dict, List, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ExcelUploadResponse(BaseModel):
    """Excel上传响应"""

    success: bool = Field(..., description="是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="Excel解析后的数据")
    error: Optional[str] = Field(None, description="错误信息")


class ExcelData(BaseModel):
    """Excel数据结构"""

    languages: List[str] = Field(..., description="语言代码列表")
    language_names: Dict[str, str] = Field(..., description="语言代码到中文名称的映射")
    translations: Dict[str, Dict[str, str]] = Field(
        ..., description="翻译数据，key -> {lang: text}"
    )
    metadata: Dict[str, Any] = Field(..., description="元数据")


class TranslationConfig(BaseModel):
    """翻译配置"""

    style: str = Field("formal", description="翻译风格: formal, informal, casual")
    domain: str = Field("general", description="翻译领域: general, technical, business")
    preserve_formatting: bool = Field(True, description="是否保持格式")


class TranslationItem(BaseModel):
    """翻译项目"""

    id: int = Field(..., description="项目ID")
    text: str = Field(..., description="待翻译文本", min_length=1)
    target_languages: List[str] = Field(..., description="目标语言列表", min_items=1)


class BatchTranslateRequest(BaseModel):
    """批量翻译请求"""

    data: List[TranslationItem] = Field(..., description="翻译数据列表", min_items=1)
    config: TranslationConfig = Field(..., description="翻译配置")


class BatchTranslateResponse(BaseModel):
    """批量翻译响应"""

    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    total_translations: int = Field(..., description="总翻译数量")


class TranslationTask(BaseModel):
    """翻译任务"""

    id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态: processing, completed, failed")
    total_records: int = Field(..., description="总记录数")
    completed_records: int = Field(0, description="已完成记录数")
    failed_records: int = Field(0, description="失败记录数")
    config: Dict[str, Any] = Field(..., description="翻译配置")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class TranslationHistoryResponse(BaseModel):
    """翻译历史响应"""

    history: List[TranslationTask] = Field(..., description="翻译历史列表")


class TranslationResultItem(BaseModel):
    """翻译结果项"""

    target_language: str = Field(..., description="目标语言")
    translated_text: Optional[str] = Field(None, description="翻译文本")
    status: str = Field(..., description="翻译状态: completed, failed, pending")
    error_message: Optional[str] = Field(None, description="错误信息")


class TranslationResult(BaseModel):
    """翻译结果"""

    source_id: int = Field(..., description="源文本ID")
    source_text: str = Field(..., description="源文本")
    translations: List[TranslationResultItem] = Field(..., description="翻译结果列表")


class TranslationResultsResponse(BaseModel):
    """翻译结果响应"""

    task_id: str = Field(..., description="任务ID")
    results: List[TranslationResult] = Field(..., description="翻译结果列表")
