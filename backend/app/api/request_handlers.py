"""
请求处理器 - 专门负责从Flask request中提取和验证数据
"""

import uuid
from typing import Dict, Any
from werkzeug.utils import secure_filename
from flask import Request

from app.models.dto import (
    FileUploadRequest,
    BatchTranslateRequest,
    PaginationRequest,
    TaskIdRequest,
)


class RequestValidationError(Exception):
    """请求验证错误"""

    pass


class RequestHandlers:
    """请求处理器集合"""

    @staticmethod
    def handle_file_upload(request: Request) -> FileUploadRequest:
        """处理文件上传请求 - 通用文件上传，不做类型限制"""
        if "file" not in request.files:
            raise RequestValidationError("请求中没有文件")

        file = request.files["file"]

        if file.filename == "":
            raise RequestValidationError("未选择文件")

        filename = secure_filename(file.filename)
        file_content = file.read()

        # 获取文件大小和MIME类型
        file_size = len(file_content)
        content_type = file.content_type

        return FileUploadRequest(
            filename=filename,
            file_content=file_content,
            original_filename=file.filename,
            file_size=file_size,
            content_type=content_type,
        )

    @staticmethod
    def handle_batch_translate(request: Request) -> BatchTranslateRequest:
        """处理批量翻译请求"""
        if not request.json:
            raise RequestValidationError("请求体不能为空")

        data = request.json

        # 注释: 支持新的JSON格式
        if "batches" in data and isinstance(data["batches"], list):
            # 新格式: { config: {...}, batches: [...] }
            if not data["batches"]:
                raise RequestValidationError("翻译批次不能为空")

            # 验证每个批次
            translation_items = []
            for batch_idx, batch in enumerate(data["batches"]):
                if "batchId" not in batch:
                    raise RequestValidationError(
                        f"第{batch_idx+1}个批次缺少batchId字段"
                    )
                if "items" not in batch or not isinstance(batch["items"], list):
                    raise RequestValidationError(
                        f"第{batch_idx+1}个批次缺少items字段或items不是数组"
                    )

                # 验证每个翻译项
                for item_idx, item in enumerate(batch["items"]):
                    if "key" not in item:
                        raise RequestValidationError(
                            f"批次{batch['batchId']}中第{item_idx+1}个翻译项缺少key字段"
                        )
                    if "sourceText" not in item:
                        raise RequestValidationError(
                            f"批次{batch['batchId']}中第{item_idx+1}个翻译项缺少sourceText字段"
                        )
                    if "targetLanguages" not in item or not item["targetLanguages"]:
                        raise RequestValidationError(
                            f"批次{batch['batchId']}中第{item_idx+1}个翻译项的目标语言不能为空"
                        )

                    # 转换为旧格式的翻译项
                    translation_items.append(
                        {
                            "id": f"{batch['batchId']}_{item['key']}",
                            "text": item["sourceText"],
                            "source_language": item.get("sourceLanguage", "auto"),
                            "target_languages": item["targetLanguages"],
                        }
                    )

            return BatchTranslateRequest(
                translation_data=translation_items, config=data.get("config", {})
            )

        # 兼容旧格式
        elif "data" in data and isinstance(data["data"], list):
            if not data["data"]:
                raise RequestValidationError("翻译数据不能为空")

            # 验证每个翻译项
            for i, item in enumerate(data["data"]):
                if "id" not in item:
                    raise RequestValidationError(f"第{i+1}个翻译项缺少id字段")
                if "text" not in item:
                    raise RequestValidationError(f"第{i+1}个翻译项缺少text字段")
                if "target_languages" not in item or not item["target_languages"]:
                    raise RequestValidationError(f"第{i+1}个翻译项的目标语言不能为空")

            return BatchTranslateRequest(
                translation_data=data["data"], config=data.get("config", {})
            )
        else:
            raise RequestValidationError("请求格式错误，缺少batches或data字段")

    @staticmethod
    def handle_pagination(request: Request) -> PaginationRequest:
        """处理分页请求"""
        try:
            limit = request.args.get("limit", 50, type=int)
            offset = request.args.get("offset", 0, type=int)
        except (ValueError, TypeError):
            raise RequestValidationError("分页参数格式错误")

        # 限制分页参数范围
        limit = min(max(limit, 1), 100)  # 限制在1-100之间
        offset = max(offset, 0)  # 不能小于0

        return PaginationRequest(limit=limit, offset=offset)

    @staticmethod
    def handle_task_id(task_id: str) -> TaskIdRequest:
        """处理任务ID请求"""
        if not task_id or not task_id.strip():
            raise RequestValidationError("任务ID不能为空")

        # 验证任务ID格式 (UUID格式)
        try:
            uuid.UUID(task_id)
        except ValueError:
            raise RequestValidationError("任务ID格式无效")

        return TaskIdRequest(task_id=task_id.strip())
