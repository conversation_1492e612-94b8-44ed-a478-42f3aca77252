"""
配置管理API接口
"""

from flask import Blueprint, request, jsonify
import logging

from app.models.common import ApiResponse
from app.services.configuration_service import ConfigurationService

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
config_bp = Blueprint("configuration", __name__)

# 初始化服务
config_service = ConfigurationService()


def handle_api_response(response: ApiResponse):
    """处理API响应的统一函数"""
    if response.success:
        return jsonify(response.to_dict()), 200
    else:
        return jsonify(response.to_dict()), 400


@config_bp.route("/config", methods=["GET"])
def get_all_configs():
    """获取所有配置"""
    try:
        configs = config_service.get_all_configs()
        return (
            jsonify(
                ApiResponse.success(
                    data=[config.dict() for config in configs],
                    message="获取配置列表成功",
                ).to_dict()
            ),
            200,
        )

    except Exception as e:
        logger.error(f"获取配置列表失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config/<string:key>", methods=["GET"])
def get_config(key: str):
    """根据key获取配置"""
    try:
        config = config_service.get_config(key)
        if config:
            return (
                jsonify(
                    ApiResponse.success(
                        data=config.dict(), message="获取配置成功"
                    ).to_dict()
                ),
                200,
            )
        else:
            return jsonify(ApiResponse.error(message="配置不存在").to_dict()), 404

    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config", methods=["POST"])
def create_config():
    """创建配置"""
    try:
        if not request.json:
            return jsonify(ApiResponse.error(message="请求体不能为空").to_dict()), 400

        key = request.json.get("key")
        value = request.json.get("value")

        if not key:
            return jsonify(ApiResponse.error(message="key字段不能为空").to_dict()), 400

        config = config_service.create_config(key, value)

        if config:
            return (
                jsonify(
                    ApiResponse.success(
                        data=config.dict(), message="创建配置成功"
                    ).to_dict()
                ),
                201,
            )
        else:
            return jsonify(ApiResponse.error(message="创建配置失败").to_dict()), 400

    except ValueError as e:
        logger.warning(f"创建配置请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"创建配置失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config/<string:key>", methods=["PUT"])
def update_config(key: str):
    """更新配置"""
    try:
        if not request.json:
            return jsonify(ApiResponse.error(message="请求体不能为空").to_dict()), 400

        value = request.json.get("value")
        config = config_service.update_config(key, value)

        if config:
            return (
                jsonify(
                    ApiResponse.success(
                        data=config.dict(), message="更新配置成功"
                    ).to_dict()
                ),
                200,
            )
        else:
            return (
                jsonify(ApiResponse.error(message="配置不存在或更新失败").to_dict()),
                404,
            )

    except ValueError as e:
        logger.warning(f"更新配置请求验证失败: {str(e)}")
        return jsonify(ApiResponse.error(message=str(e)).to_dict()), 400
    except Exception as e:
        logger.error(f"更新配置失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config/<string:key>", methods=["DELETE"])
def delete_config(key: str):
    """删除配置"""
    try:
        success = config_service.delete_config(key)

        if success:
            return jsonify(ApiResponse.success(message="删除配置成功").to_dict()), 200
        else:
            return (
                jsonify(ApiResponse.error(message="配置不存在或删除失败").to_dict()),
                404,
            )

    except Exception as e:
        logger.error(f"删除配置失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config/<string:key>/value", methods=["GET"])
def get_config_value(key: str):
    """获取配置值"""
    try:
        value = config_service.get_config_value(key)

        return (
            jsonify(
                ApiResponse.success(
                    data={"key": key, "value": value}, message="获取配置值成功"
                ).to_dict()
            ),
            200,
        )

    except Exception as e:
        logger.error(f"获取配置值失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )


@config_bp.route("/config/<string:key>/value", methods=["PUT"])
def set_config_value(key: str):
    """设置配置值"""
    try:
        if not request.json or "value" not in request.json:
            return (
                jsonify(ApiResponse.error(message="请求体必须包含value字段").to_dict()),
                400,
            )

        value = request.json["value"]
        success = config_service.set_config_value(key, value)

        if success:
            return (
                jsonify(
                    ApiResponse.success(
                        data={"key": key, "value": value}, message="设置配置值成功"
                    ).to_dict()
                ),
                200,
            )
        else:
            return jsonify(ApiResponse.error(message="设置配置值失败").to_dict()), 400

    except Exception as e:
        logger.error(f"设置配置值失败: {str(e)}")
        return (
            jsonify(ApiResponse.error(message=f"服务器内部错误: {str(e)}").to_dict()),
            500,
        )
