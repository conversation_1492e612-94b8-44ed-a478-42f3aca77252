"""
配置服务
"""

import logging
from typing import Optional, List
from backend.app.helpers.database_helper import (
    fetch_one,
    fetch_all,
    execute_returning,
    execute,
)
from app.models.configuration import Configuration

logger = logging.getLogger(__name__)


class ConfigurationService:
    """配置服务类"""

    def get_config(self, key: str) -> Optional[Configuration]:
        """根据key获取配置"""
        try:
            sql = "SELECT id, key, value, create_time, update_time FROM configuration WHERE key = %s"
            row = fetch_one(sql, (key,))

            if row:
                return Configuration(
                    id=row[0],
                    key=row[1],
                    value=row[2],
                    create_time=row[3],
                    update_time=row[4],
                )
            return None
        except Exception as e:
            logger.error(f"获取配置失败: {str(e)}")
            return None

    def get_all_configs(self) -> List[Configuration]:
        """获取所有配置"""
        try:
            sql = "SELECT id, key, value, create_time, update_time FROM configuration ORDER BY key"
            rows = fetch_all(sql)

            configs = []
            for row in rows:
                configs.append(
                    Configuration(
                        id=row[0],
                        key=row[1],
                        value=row[2],
                        create_time=row[3],
                        update_time=row[4],
                    )
                )
            return configs
        except Exception as e:
            logger.error(f"获取所有配置失败: {str(e)}")
            return []

    def create_config(
        self, key: str, value: Optional[str] = None
    ) -> Optional[Configuration]:
        """创建配置"""
        try:
            sql = """
                INSERT INTO configuration (key, value)
                VALUES (%s, %s)
                RETURNING id, key, value, create_time, update_time
            """
            row = execute_returning(sql, (key, value))

            if row:
                logger.info(f"创建配置成功: {key}")
                return Configuration(
                    id=row[0],
                    key=row[1],
                    value=row[2],
                    create_time=row[3],
                    update_time=row[4],
                )
            return None
        except Exception as e:
            logger.error(f"创建配置失败: {str(e)}")
            return None

    def update_config(self, key: str, value: Optional[str]) -> Optional[Configuration]:
        """更新配置"""
        try:
            sql = """
                UPDATE configuration
                SET value = %s, update_time = CURRENT_TIMESTAMP
                WHERE key = %s
                RETURNING id, key, value, create_time, update_time
            """
            row = execute_returning(sql, (value, key))

            if row:
                logger.info(f"更新配置成功: {key}")
                return Configuration(
                    id=row[0],
                    key=row[1],
                    value=row[2],
                    create_time=row[3],
                    update_time=row[4],
                )
            return None
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            return None

    def delete_config(self, key: str) -> bool:
        """删除配置"""
        try:
            sql = "DELETE FROM configuration WHERE key = %s"
            affected_rows = execute(sql, (key,))

            if affected_rows > 0:
                logger.info(f"删除配置成功: {key}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除配置失败: {str(e)}")
            return False

    def get_config_value(
        self, key: str, default: Optional[str] = None
    ) -> Optional[str]:
        """
        获取配置值的便捷方法

        Args:
            key: 配置键名
            default: 默认值

        Returns:
            str: 配置值
        """
        config = self.get_config(key)
        if config and config.value is not None:
            return config.value
        return default

    def set_config_value(self, key: str, value: str) -> bool:
        """
        设置配置值的便捷方法

        Args:
            key: 配置键名
            value: 配置值

        Returns:
            bool: 是否设置成功
        """
        # 先尝试更新
        updated_config = self.update_config(key, value)
        if updated_config:
            return True

        # 如果更新失败，尝试创建
        created_config = self.create_config(key, value)
        return created_config is not None
