"""
服务模块
"""
try:
    from .translate_service import TranslateService
except ImportError as e:
    print(f"Warning: Could not import TranslateService: {e}")
    TranslateService = None

try:
    from .excel_service import ExcelService
except ImportError as e:
    print(f"Warning: Could not import ExcelService: {e}")
    ExcelService = None

try:
    from .prompt_manager import PromptManager, TranslationConfig
except ImportError as e:
    print(f"Warning: Could not import PromptManager: {e}")
    PromptManager = None
    TranslationConfig = None

try:
    from .langchain_service import LangchainService
except ImportError as e:
    print(f"Warning: Could not import LangchainService: {e}")
    LangchainService = None

__all__ = ["TranslateService", "ExcelService", "PromptManager", "TranslationConfig", "LangchainService"]