"""
提示词管理服务
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class TranslationConfig:
    """翻译配置"""

    style: str = "formal"  # formal, informal, casual
    domain: str = "general"  # general, technical, business
    preserve_formatting: bool = True


class PromptManager:
    """提示词管理器"""

    def __init__(self):
        """初始化提示词管理器"""
        self.base_templates = {
            "formal": {
                "general": """你是一个专业的翻译助手。请将以下文本从{source_language}翻译成{target_language}。

要求：
1. 保持正式、专业的语调
2. 确保翻译准确、流畅
3. 保持原文的含义和语境
4. 使用标准的{target_language}表达方式

原文：{source_text}

请直接提供翻译结果，不要包含任何解释或额外信息：""",
                "technical": """你是一个专业的技术翻译专家。请将以下技术文档从{source_language}翻译成{target_language}。

要求：
1. 保持技术术语的准确性
2. 使用正式的技术文档语调
3. 保持专业术语的一致性
4. 确保技术概念的准确传达

原文：{source_text}

请直接提供翻译结果，不要包含任何解释或额外信息：""",
                "business": """你是一个专业的商务翻译专家。请将以下商务文档从{source_language}翻译成{target_language}。

要求：
1. 使用正式的商务语调
2. 保持商务术语的准确性
3. 确保语言得体、专业
4. 符合商务沟通规范

原文：{source_text}

请直接提供翻译结果，不要包含任何解释或额外信息：""",
            },
            "informal": {
                "general": """请将以下文本从{source_language}翻译成{target_language}。

要求：
1. 使用自然、轻松的语调
2. 保持原文的情感色彩
3. 使用日常用语表达
4. 确保翻译自然流畅

原文：{source_text}

请直接提供翻译结果：""",
                "technical": """请将以下技术内容从{source_language}翻译成{target_language}。

要求：
1. 保持技术准确性的同时使用易懂的表达
2. 适当简化复杂的技术术语
3. 使用相对轻松的语调
4. 确保内容易于理解

原文：{source_text}

请直接提供翻译结果：""",
                "business": """请将以下商务内容从{source_language}翻译成{target_language}。

要求：
1. 使用友好但专业的语调
2. 保持商务内容的准确性
3. 适当使用亲和的表达方式
4. 确保沟通效果良好

原文：{source_text}

请直接提供翻译结果：""",
            },
            "casual": {
                "general": """帮我把这段话从{source_language}翻译成{target_language}：

{source_text}

要求：
- 用轻松自然的语调
- 保持原文的感觉
- 不要太正式

翻译：""",
                "technical": """帮我把这段技术内容从{source_language}翻译成{target_language}：

{source_text}

要求：
- 保持技术准确性
- 用比较轻松的方式表达
- 让人容易理解

翻译：""",
                "business": """帮我把这段商务内容从{source_language}翻译成{target_language}：

{source_text}

要求：
- 保持专业但不要太严肃
- 用友好的语调
- 确保意思准确

翻译：""",
            },
        }

        # 语言代码到语言名称的映射
        self.language_names = {
            "zh": "中文",
            "en": "英语",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "it": "意大利语",
            "pt": "葡萄牙语",
            "ru": "俄语",
            "ar": "阿拉伯语",
            "th": "泰语",
            "vi": "越南语",
        }

    def create_prompt_template(self, config: TranslationConfig) -> str:
        """
        根据配置创建提示词模板

        Args:
            config: 翻译配置

        Returns:
            str: 提示词模板
        """
        style = config.style if config.style in self.base_templates else "formal"
        domain = (
            config.domain if config.domain in self.base_templates[style] else "general"
        )

        return self.base_templates[style][domain]

    def format_prompt(
        self,
        template: str,
        source_text: str,
        source_language: str = "自动检测",
        target_language: str = "中文",
    ) -> str:
        """
        格式化提示词

        Args:
            template: 提示词模板
            source_text: 源文本
            source_language: 源语言代码或名称
            target_language: 目标语言代码或名称

        Returns:
            str: 格式化后的提示词
        """
        # 转换语言代码为语言名称
        source_lang_name = self.language_names.get(source_language, source_language)
        target_lang_name = self.language_names.get(target_language, target_language)

        return template.format(
            source_text=source_text,
            source_language=source_lang_name,
            target_language=target_lang_name,
        )

    def get_available_domains(self, style: str = "formal") -> list:
        """获取指定风格下可用的领域"""
        if style in self.base_templates:
            return list(self.base_templates[style].keys())
        return list(self.base_templates["formal"].keys())

    @staticmethod
    def get_translate_language_codes_prompt(language_codes: list) -> str:
        """
        获取翻译语言代码的提示词

        Args:
            language_codes: 语言代码列表

        Returns:
            str: 格式化的提示词
        """
        return f"""请将以下语言代码转换为对应的中文语言名称，并以JSON格式返回。

语言代码列表：{language_codes}

要求：
1. 返回标准的JSON格式
2. 键为原始语言代码，值为对应的中文语言名称
3. 如果遇到不认识的语言代码，请返回"未知语言"
4. 只返回JSON，不要包含任何其他文字

示例格式：
{{"en-US": "英语", "zh-CN": "中文", "ja-JP": "日语"}}

请直接返回JSON结果
"""

    def validate_config(self, config: TranslationConfig) -> bool:
        """
        验证翻译配置是否有效

        Args:
            config: 翻译配置

        Returns:
            bool: 配置是否有效
        """
        if config.style not in self.base_templates:
            return False

        if config.domain not in self.base_templates[config.style]:
            return False

        return True

    def create_formatted_prompt(
        self,
        source_text: str,
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> str:
        """
        创建完整的格式化提示词

        Args:
            source_text: 源文本
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            str: 完整的格式化提示词
        """
        template = self.create_prompt_template(config)
        return self.format_prompt(
            template, source_text, source_language, target_language
        )
