"""
翻译服务层
"""

import logging
import uuid
from typing import Optional, List, Dict, Any

# 移除单条翻译相关导入，因为已经去除单条翻译功能
from app.services.langchain_service import LangchainService, TranslationResult
from app.services.prompt_manager import TranslationConfig
from app.models.translation import (
    BatchTranslateResponse,
    TranslationHistoryResponse,
    TranslationResultsResponse,
    TranslationTask,
    TranslationResult as TranslationResultModel,
    TranslationResultItem,
)
from app.models.common import ApiResponse
from app.models.translation_task import TranslationTask as TranslationTaskModel
from app.models.translation_record import TranslationRecord

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TranslationError(Exception):
    """翻译错误类"""

    def __init__(self, message: str, error_type: str, record_id: Optional[int] = None):
        self.message = message
        self.error_type = error_type  # network, api, validation, system
        self.record_id = record_id
        super().__init__(message)


class TranslateService:
    """翻译服务类"""

    def __init__(self):
        """初始化翻译服务"""
        self.langchain_service = None
        self._initialize_langchain()

    def _initialize_langchain(self):
        """初始化Langchain服务"""
        try:
            self.langchain_service = LangchainService()
            logger.info("Langchain服务初始化成功")
        except Exception as e:
            logger.error(f"Langchain服务初始化失败: {str(e)}")
            # 在开发环境中可以继续使用mock翻译
            self.langchain_service = None

    def translate_single_record(
        self,
        source_text: str,
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> TranslationResult:
        """
        翻译单条记录（新接口）

        Args:
            source_text: 源文本
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            TranslationResult: 翻译结果
        """
        if self.langchain_service:
            return self.langchain_service.translate_single_record(
                source_text=source_text,
                target_language=target_language,
                config=config,
                source_language=source_language,
            )
        else:
            # 降级到mock翻译
            try:
                translated_text = self._mock_translate(
                    source_text, source_language, target_language
                )
                return TranslationResult(
                    success=True,
                    translated_text=translated_text,
                    source_text=source_text,
                    target_language=target_language,
                )
            except Exception as e:
                return TranslationResult(
                    success=False,
                    error_message=str(e),
                    source_text=source_text,
                    target_language=target_language,
                )

    def batch_translate(
        self,
        texts: List[str],
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> List[TranslationResult]:
        """
        批量翻译文本

        Args:
            texts: 待翻译文本列表
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            List[TranslationResult]: 翻译结果列表
        """
        if self.langchain_service:
            return self.langchain_service.batch_translate(
                texts=texts,
                target_language=target_language,
                config=config,
                source_language=source_language,
            )
        else:
            # 降级到mock翻译
            results = []
            for text in texts:
                try:
                    translated_text = self._mock_translate(
                        text, source_language, target_language
                    )
                    results.append(
                        TranslationResult(
                            success=True,
                            translated_text=translated_text,
                            source_text=text,
                            target_language=target_language,
                        )
                    )
                except Exception as e:
                    results.append(
                        TranslationResult(
                            success=False,
                            error_message=str(e),
                            source_text=text,
                            target_language=target_language,
                        )
                    )
            return results

    def get_available_domains(self, style: str = "formal") -> List[str]:
        """获取可用的翻译领域"""
        if self.langchain_service:
            return self.langchain_service.get_available_domains(style)
        else:
            return ["general", "technical", "business"]

    def start_batch_translation_task(
        self, translation_items: List[Any], config: Dict[str, Any], db_connection
    ) -> tuple[str, int]:
        """
        启动批量翻译任务

        Args:
            translation_items: 翻译项目列表
            config: 翻译配置
            db_connection: 数据库连接

        Returns:
            tuple[str, int]: (任务ID, 总翻译数量)
        """
        from app.models.translation_task import TranslationTask
        from app.models.translation_record import TranslationRecord

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 计算总翻译数量
        total_translations = sum(
            len(item.target_languages) for item in translation_items
        )

        # 创建翻译任务
        TranslationTask.create(task_id, total_translations, config, db_connection)

        # 创建翻译记录
        TranslationRecord.create_batch(task_id, translation_items, db_connection)

        logger.info(f"批量翻译任务已创建: {task_id}, 总翻译数量: {total_translations}")

        return task_id, total_translations
