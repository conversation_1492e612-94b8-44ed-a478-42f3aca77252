"""
翻译服务层
"""
import logging
import uuid
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from pydantic import ValidationError

# 移除单条翻译相关导入，因为已经去除单条翻译功能
from app.services.langchain_service import LangchainService, TranslationResult
from app.services.prompt_manager import TranslationConfig
from app.models.translation import (
    BatchTranslateResponse, TranslationHistoryResponse,
    TranslationResultsResponse, TranslationTask, TranslationResult as TranslationResultModel,
    TranslationResultItem
)
from app.models.common import ApiResponse
from app.models.translation_task import TranslationTask as TranslationTaskModel
from app.models.translation_record import TranslationRecord
from app.config.database import get_db_connection
from app.models.dto import (
    TranslationItem, TranslationConfig as DTOTranslationConfig,
    BatchTranslateRequest, TranslationTaskRequest, TranslationTaskResponse
)


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TranslationError(Exception):
    """翻译错误类"""
    def __init__(self, message: str, error_type: str, record_id: Optional[int] = None):
        self.message = message
        self.error_type = error_type  # network, api, validation, system
        self.record_id = record_id
        super().__init__(message)


class TranslateService:
    """翻译服务类"""

    def __init__(self):
        """初始化翻译服务"""
        self.langchain_service = None
        self._initialize_langchain()

    def _initialize_langchain(self):
        """初始化Langchain服务"""
        try:
            self.langchain_service = LangchainService()
            logger.info("Langchain服务初始化成功")
        except Exception as e:
            logger.error(f"Langchain服务初始化失败: {str(e)}")
            # 在开发环境中可以继续使用mock翻译
            self.langchain_service = None



    def translate_single_record(self,
                               source_text: str,
                               target_language: str,
                               config: TranslationConfig,
                               source_language: str = "自动检测") -> TranslationResult:
        """
        翻译单条记录（新接口）

        Args:
            source_text: 源文本
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            TranslationResult: 翻译结果
        """
        if self.langchain_service:
            return self.langchain_service.translate_single_record(
                source_text=source_text,
                target_language=target_language,
                config=config,
                source_language=source_language
            )
        else:
            # 降级到mock翻译
            try:
                translated_text = self._mock_translate(
                    source_text,
                    source_language,
                    target_language
                )
                return TranslationResult(
                    success=True,
                    translated_text=translated_text,
                    source_text=source_text,
                    target_language=target_language
                )
            except Exception as e:
                return TranslationResult(
                    success=False,
                    error_message=str(e),
                    source_text=source_text,
                    target_language=target_language
                )

    def batch_translate(self,
                       texts: List[str],
                       target_language: str,
                       config: TranslationConfig,
                       source_language: str = "自动检测") -> List[TranslationResult]:
        """
        批量翻译文本

        Args:
            texts: 待翻译文本列表
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            List[TranslationResult]: 翻译结果列表
        """
        if self.langchain_service:
            return self.langchain_service.batch_translate(
                texts=texts,
                target_language=target_language,
                config=config,
                source_language=source_language
            )
        else:
            # 降级到mock翻译
            results = []
            for text in texts:
                try:
                    translated_text = self._mock_translate(text, source_language, target_language)
                    results.append(TranslationResult(
                        success=True,
                        translated_text=translated_text,
                        source_text=text,
                        target_language=target_language
                    ))
                except Exception as e:
                    results.append(TranslationResult(
                        success=False,
                        error_message=str(e),
                        source_text=text,
                        target_language=target_language
                    ))
            return results



    def get_supported_languages(self) -> Dict[str, str]:
        """获取支持的语言列表"""
        if self.langchain_service:
            return self.langchain_service.get_supported_languages()
        else:
            # 返回基本的语言支持
            return {
                "zh": "中文",
                "en": "英语",
                "ja": "日语",
                "ko": "韩语",
                "fr": "法语",
                "de": "德语",
                "es": "西班牙语"
            }

    def get_available_styles(self) -> List[str]:
        """获取可用的翻译风格"""
        if self.langchain_service:
            return self.langchain_service.get_available_styles()
        else:
            return ["formal", "informal", "casual"]

    def get_available_domains(self, style: str = "formal") -> List[str]:
        """获取可用的翻译领域"""
        if self.langchain_service:
            return self.langchain_service.get_available_domains(style)
        else:
            return ["general", "technical", "business"]

    def start_batch_translation_task(self, translation_items: List[Any], config: Dict[str, Any], db_connection) -> tuple[str, int]:
        """
        启动批量翻译任务

        Args:
            translation_items: 翻译项目列表
            config: 翻译配置
            db_connection: 数据库连接

        Returns:
            tuple[str, int]: (任务ID, 总翻译数量)
        """
        from app.models.translation_task import TranslationTask
        from app.models.translation_record import TranslationRecord

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 计算总翻译数量
        total_translations = sum(len(item.target_languages) for item in translation_items)

        # 创建翻译任务
        TranslationTask.create(task_id, total_translations, config, db_connection)

        # 创建翻译记录
        TranslationRecord.create_batch(task_id, translation_items, db_connection)

        logger.info(f"批量翻译任务已创建: {task_id}, 总翻译数量: {total_translations}")

        return task_id, total_translations



    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取翻译任务状态

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        from app.config.database import get_db_connection
        from app.models.translation_task import TranslationTask
        from app.models.translation_record import TranslationRecord

        db_connection = None
        try:
            db_connection = get_db_connection()

            # 获取任务信息
            task = TranslationTask.get_by_id(task_id, db_connection)
            if not task:
                return None

            # 获取统计信息
            stats = TranslationRecord.get_task_statistics(task_id, db_connection)

            return {
                "task_id": task.id,
                "status": task.status,
                "total_records": task.total_records,
                "completed_records": stats.get("completed", 0),
                "failed_records": stats.get("failed", 0),
                "pending_records": stats.get("pending", 0),
                "processing_records": stats.get("processing", 0),
                "progress_percentage": task.get_progress_percentage(),
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "config": task.config.to_dict() if task.config else {}
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return None
        finally:
            if db_connection:
                try:
                    db_connection.close()
                except Exception:
                    pass

    def start_batch_translation(self, batch_request: BatchTranslateRequest) -> ApiResponse:
        """
        启动批量翻译任务

        Args:
            batch_request: 批量翻译请求DTO

        Returns:
            ApiResponse: 统一响应格式
        """
        try:
            # 使用DTO的转换方法
            translation_items = batch_request.to_translation_items()
            translation_config = batch_request.to_translation_config()

            # 启动批量翻译任务
            db_connection = get_db_connection()
            try:
                task_id, total_translations = self.start_batch_translation_task(
                    translation_items,
                    batch_request.config,  # 使用原始配置字典
                    db_connection
                )

                response_data = BatchTranslateResponse(
                    task_id=task_id,
                    status="processing",
                    message="翻译任务已启动",
                    total_translations=total_translations
                )

                logger.info(f"批量翻译任务启动成功: {task_id}, 总翻译数量: {total_translations}")
                return ApiResponse.success(response_data.dict(), "翻译任务已启动")
            finally:
                db_connection.close()

        except Exception as e:
            logger.error(f"启动批量翻译任务失败: {str(e)}")
            return ApiResponse.error(message=f"启动翻译任务失败: {str(e)}")

    def get_translation_history(self, limit: int, offset: int) -> ApiResponse:
        """
        获取翻译历史

        Args:
            limit: 每页数量
            offset: 偏移量

        Returns:
            ApiResponse: 统一响应格式
        """
        try:
            db_connection = get_db_connection()
            try:
                # 获取翻译任务历史
                tasks = TranslationTaskModel.get_all(db_connection, limit=limit, offset=offset)

                # 转换为DTO
                task_dtos = []
                for task in tasks:
                    try:
                        task_dto = TranslationTask(
                            id=task.id,
                            status=task.status,
                            total_records=task.total_records,
                            completed_records=task.completed_records,
                            failed_records=task.failed_records,
                            config=task.config.to_dict() if task.config else {},
                            created_at=task.created_at.isoformat() if task.created_at else "",
                            updated_at=task.updated_at.isoformat() if task.updated_at else ""
                        )
                        task_dtos.append(task_dto)
                    except Exception as e:
                        logger.warning(f"跳过无效的任务记录 {task.id}: {str(e)}")
                        continue

                response_data = TranslationHistoryResponse(history=task_dtos)
                logger.info(f"成功获取翻译历史: {len(task_dtos)} 条记录")
                return ApiResponse.success(response_data.dict(), "获取翻译历史成功")
            finally:
                db_connection.close()

        except Exception as e:
            logger.error(f"获取翻译历史失败: {str(e)}")
            return ApiResponse.error(message=f"获取翻译历史失败: {str(e)}")

    def get_translation_results(self, task_id: str) -> ApiResponse:
        """
        获取翻译结果

        Args:
            task_id: 任务ID

        Returns:
            ApiResponse: 统一响应格式
        """
        try:
            # 验证任务ID
            if not task_id or not task_id.strip():
                return ApiResponse.error(message="任务ID不能为空")

            # 验证任务ID格式 (UUID格式)
            import uuid
            try:
                uuid.UUID(task_id)
            except ValueError:
                return ApiResponse.error(message="任务ID格式无效")

            db_connection = get_db_connection()
            try:
                # 验证任务是否存在
                task = TranslationTaskModel.get_by_id(task_id, db_connection)
                if not task:
                    return ApiResponse.error(message="任务不存在")

                # 获取翻译记录
                records = TranslationRecord.get_by_task_id(task_id, db_connection)

                # 按source_id分组整理结果
                results_dict = {}
                for record in records:
                    try:
                        source_id = record.source_id
                        if source_id not in results_dict:
                            results_dict[source_id] = {
                                'source_id': source_id,
                                'source_text': record.source_text,
                                'translations': []
                            }

                        # 添加翻译结果
                        translation_item = TranslationResultItem(
                            target_language=record.target_language,
                            translated_text=record.translated_text,
                            status=record.status,
                            error_message=record.error_message
                        )
                        results_dict[source_id]['translations'].append(translation_item)
                    except Exception as e:
                        logger.warning(f"跳过无效的翻译记录 {record.id}: {str(e)}")
                        continue

                # 转换为结果列表
                results = []
                for source_id in sorted(results_dict.keys()):
                    try:
                        result_data = results_dict[source_id]
                        result_dto = TranslationResultModel(
                            source_id=result_data['source_id'],
                            source_text=result_data['source_text'],
                            translations=result_data['translations']
                        )
                        results.append(result_dto)
                    except Exception as e:
                        logger.warning(f"跳过无效的结果数据 {source_id}: {str(e)}")
                        continue

                response_data = TranslationResultsResponse(
                    task_id=task_id,
                    results=results
                )

                logger.info(f"成功获取翻译结果: 任务 {task_id}, {len(results)} 个源文本")
                return ApiResponse.success(response_data.dict(), "获取翻译结果成功")
            finally:
                db_connection.close()

        except Exception as e:
            logger.error(f"获取翻译结果失败: {str(e)}")
            return ApiResponse.error(message=f"获取翻译结果失败: {str(e)}")

    def _mock_translate(self, text: str, source_lang: Optional[str], target_lang: str) -> str:
        """
        模拟翻译功能
        实际项目中这里应该调用真实的翻译API
        """
        # 简单的模拟翻译
        if target_lang == "zh":
            return f"[翻译为中文] {text}"
        elif target_lang == "en":
            return f"[Translated to English] {text}"
        else:
            return f"[Translated to {target_lang}] {text}"