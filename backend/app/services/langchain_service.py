"""
Langchain集成服务
"""

import os
import json
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from langchain.schema import HumanMessage

from app.services.prompt_manager import PromptManager, TranslationConfig
from app.llms import LLMType, get_llm


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TranslationResult:
    """翻译结果"""

    success: bool
    translated_text: Optional[str] = None
    error_message: Optional[str] = None
    source_text: Optional[str] = None
    target_language: Optional[str] = None


class LangchainService:
    """Langchain集成服务"""

    def __init__(
        self, api_key: Optional[str] = None, llm_type: LLMType = LLMType.TONGYI
    ):
        """
        初始化Langchain服务

        Args:
            api_key: API密钥，如果不提供则从环境变量获取
            llm_type: 大模型类型，默认为通义千问
        """
        self.api_key = api_key or os.getenv("QWEN_API_KEY")
        self.llm_type = llm_type
        self.prompt_manager = PromptManager()
        self.llm = None
        self._initialize_llm()

    def _initialize_llm(self):
        """初始化LLM实例"""
        try:
            self.llm = get_llm(llm_type=self.llm_type, api_key=self.api_key)
            logger.info(f"LLM初始化成功: {self.llm_type.value}")
        except Exception as e:
            logger.error(f"初始化LLM失败: {str(e)}")
            raise

    def translate_single_record(
        self,
        source_text: str,
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> TranslationResult:
        """
        翻译单条记录

        Args:
            source_text: 源文本
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            TranslationResult: 翻译结果
        """
        try:
            # 验证配置
            if not self.prompt_manager.validate_config(config):
                return TranslationResult(
                    success=False,
                    error_message="无效的翻译配置",
                    source_text=source_text,
                    target_language=target_language,
                )

            # 创建提示词
            prompt = self.prompt_manager.create_formatted_prompt(
                source_text=source_text,
                target_language=target_language,
                config=config,
                source_language=source_language,
            )

            logger.info(f"开始翻译: {source_text[:50]}... -> {target_language}")

            # 调用LLM进行翻译
            messages = [HumanMessage(content=prompt)]
            response = self.llm(messages)
            translated_text = response.content

            # 后处理翻译结果
            translated_text = self._post_process_translation(
                translated_text, config.preserve_formatting
            )

            logger.info(f"翻译完成: {translated_text[:50]}...")

            return TranslationResult(
                success=True,
                translated_text=translated_text,
                source_text=source_text,
                target_language=target_language,
            )

        except Exception as e:
            error_msg = f"翻译失败: {str(e)}"
            logger.error(error_msg)
            return TranslationResult(
                success=False,
                error_message=error_msg,
                source_text=source_text,
                target_language=target_language,
            )

    def _post_process_translation(
        self, translated_text: str, preserve_formatting: bool = True
    ) -> str:
        """
        后处理翻译结果

        Args:
            translated_text: 原始翻译文本
            preserve_formatting: 是否保持格式

        Returns:
            str: 处理后的翻译文本
        """
        if not translated_text:
            return ""

        # 移除可能的前缀和后缀
        text = translated_text.strip()

        # 移除常见的AI回复前缀
        prefixes_to_remove = [
            "翻译：",
            "翻译结果：",
            "Translation:",
            "Result:",
            "答案：",
            "回答：",
            "翻译为：",
            "译文：",
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix) :].strip()
                break

        # 如果需要保持格式，进行额外处理
        if preserve_formatting:
            # 保持换行符
            text = text.replace("\\n", "\n")

            # 移除多余的空行
            lines = text.split("\n")
            cleaned_lines = []
            prev_empty = False

            for line in lines:
                line = line.strip()
                if line == "":
                    if not prev_empty:
                        cleaned_lines.append("")
                    prev_empty = True
                else:
                    cleaned_lines.append(line)
                    prev_empty = False

            text = "\n".join(cleaned_lines).strip()

        return text

    def batch_translate(
        self,
        texts: list,
        target_language: str,
        config: TranslationConfig,
        source_language: str = "自动检测",
    ) -> list:
        """
        批量翻译文本

        Args:
            texts: 待翻译文本列表
            target_language: 目标语言
            config: 翻译配置
            source_language: 源语言

        Returns:
            list: 翻译结果列表
        """
        results = []

        for text in texts:
            result = self.translate_single_record(
                source_text=text,
                target_language=target_language,
                config=config,
                source_language=source_language,
            )
            results.append(result)

        return results

    def get_language_names(self, language_codes: List[str]) -> Dict[str, str]:
        """
        通过大模型获取语言代码对应的中文名称

        Args:
            language_codes: 语言代码列表，如 ['en-US', 'zh-CN', 'ja-JP']

        Returns:
            Dict[str, str]: 语言代码到中文名称的映射，如 {'en-US': '英语', 'zh-CN': '中文'}
        """
        try:
            # 构建提示词
            prompt = PromptManager.get_translate_language_codes_prompt(language_codes)

            logger.info(f"开始获取语言名称: {language_codes}")

            # 调用LLM
            messages = [HumanMessage(content=prompt)]
            response = self.llm(messages)
            result_text = response.content.strip()

            try:
                language_names = json.loads(result_text)
                logger.info(f"成功获取语言名称: {language_names}")
                return language_names
            except json.JSONDecodeError as e:
                logger.error(f"解析语言名称JSON失败: {e}, 原始响应: {result_text}")
                # 如果解析失败，返回默认映射
                return {code: f"语言_{code}" for code in language_codes}

        except Exception as e:
            logger.error(f"获取语言名称失败: {str(e)}")
            # 如果调用失败，返回默认映射
            return {code: f"语言_{code}" for code in language_codes}

    def get_available_domains(self, style: str = "formal") -> list:
        """获取可用的翻译领域"""
        return self.prompt_manager.get_available_domains(style)
