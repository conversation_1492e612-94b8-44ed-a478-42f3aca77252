"""
应用配置模块
"""
import os


class Settings:
    """应用配置类"""

    def __init__(self):
        # Flask基础配置
        self.SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key")
        self.FLASK_ENV = os.getenv("FLASK_ENV", "development")
        self.DEBUG = os.getenv("DEBUG", "True").lower() == "true"

        # 服务器配置
        self.HOST = os.getenv("HOST", "0.0.0.0")
        self.PORT = int(os.getenv("PORT", "32982"))

        # CORS配置
        self.CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:23876")

    @property
    def cors_origins_list(self) -> list[str]:
        """获取CORS允许的源列表"""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]


# 全局配置实例
settings = Settings()