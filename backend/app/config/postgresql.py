"""
PostgreSQL数据库连接管理工具
"""

import os
import psycopg2
from psycopg2 import pool
from contextlib import contextmanager
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class PostgreSQLManager:
    """PostgreSQL数据库连接管理器"""

    def __init__(self):
        self.pool: Optional[psycopg2.pool.ThreadedConnectionPool] = None
        self._initialize_pool()

    def _initialize_pool(self):
        """初始化数据库连接池"""
        try:
            print(os.getenv("PG_USER", "postgres"), os.getenv("PG_PASSWORD", ""))
            # PostgreSQL连接配置
            config = {
                "host": os.getenv("PG_HOST", "localhost"),
                "port": int(os.getenv("PG_PORT", "5432")),
                "user": os.getenv("PG_USER", "postgres"),
                "password": os.getenv("PG_PASSWORD", ""),
                "database": os.getenv("PG_DATABASE", "petkit_translator"),
                "options": "-c client_encoding=UTF8",
            }

            # 创建连接池
            self.pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1, maxconn=int(os.getenv("PG_POOL_SIZE", "10")), **config
            )
            logger.info("PostgreSQL数据库连接池初始化成功")

        except Exception as e:
            logger.warning(f"PostgreSQL数据库连接池初始化失败: {e}")
            logger.warning("API将在无数据库模式下运行")
            self.pool = None

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            if not self.pool:
                raise Exception("数据库连接池未初始化")

            connection = self.pool.getconn()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if connection:
                self.pool.putconn(connection)

    def get_connection_direct(self):
        """直接获取数据库连接（用于服务层）"""
        if not self.pool:
            raise Exception("数据库连接池未初始化")
        return self.pool.getconn()

    def return_connection(self, connection):
        """归还数据库连接"""
        if self.pool and connection:
            self.pool.putconn(connection)

    def execute_script(self, script: str):
        """执行SQL脚本"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                # 分割脚本为单独的语句
                statements = [
                    stmt.strip() for stmt in script.split(";") if stmt.strip()
                ]

                for statement in statements:
                    cursor.execute(statement)

                conn.commit()
                logger.info("SQL脚本执行成功")

            except Exception as e:
                conn.rollback()
                logger.error(f"SQL脚本执行失败: {e}")
                raise
            finally:
                cursor.close()

    def execute_migration(self, migration_file: str):
        """执行迁移文件"""
        try:
            with open(migration_file, "r", encoding="utf-8") as f:
                script = f.read()

            self.execute_script(script)
            logger.info(f"迁移文件执行成功: {migration_file}")

        except Exception as e:
            logger.error(f"迁移文件执行失败: {migration_file}, 错误: {e}")
            raise

    def check_connection(self) -> bool:
        """检查数据库连接是否正常"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"PostgreSQL数据库连接检查失败: {e}")
            return False

    def close_all_connections(self):
        """关闭所有连接"""
        if self.pool:
            self.pool.closeall()
            logger.info("所有PostgreSQL数据库连接已关闭")


# 全局PostgreSQL数据库管理器实例
pg_manager = PostgreSQLManager()


def get_pg_connection():
    """获取PostgreSQL数据库连接（用于API层）"""
    return pg_manager.get_connection_direct()


def return_pg_connection(connection):
    """归还PostgreSQL数据库连接"""
    pg_manager.return_connection(connection)


# 为了兼容现有代码，提供一个通用的get_db_connection函数
def get_db_connection():
    """获取数据库连接（PostgreSQL版本）"""
    return get_pg_connection()
