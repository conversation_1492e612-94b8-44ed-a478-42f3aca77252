"""
统一数据库操作封装
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager
from app.config.postgresql import pg_manager

logger = logging.getLogger(__name__)


class DatabaseError(Exception):
    """数据库操作异常"""

    pass


class DatabaseHelper:
    """数据库操作封装类"""

    @staticmethod
    def execute(sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行SQL语句（INSERT, UPDATE, DELETE）

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            int: 受影响的行数

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    affected_rows = cursor.rowcount
                    conn.commit()
                    return affected_rows
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库操作失败: {str(e)}")

    @staticmethod
    def fetch_one(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
        """
        查询单条记录

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            Optional[Tuple]: 查询结果，如果没有结果返回None

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    return cursor.fetchone()
        except Exception as e:
            logger.error(f"查询单条记录失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def fetch_all(sql: str, params: Optional[Tuple] = None) -> List[Tuple]:
        """
        查询多条记录

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            List[Tuple]: 查询结果列表

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询多条记录失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def execute_returning(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
        """
        执行SQL语句并返回结果（适用于INSERT ... RETURNING）

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            Optional[Tuple]: 返回的结果

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    result = cursor.fetchone()
                    conn.commit()
                    return result
        except Exception as e:
            logger.error(f"执行SQL并返回结果失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库操作失败: {str(e)}")

    @staticmethod
    def execute_many(sql: str, params_list: List[Tuple]) -> int:
        """
        批量执行SQL语句

        Args:
            sql: SQL语句
            params_list: 参数列表

        Returns:
            int: 受影响的总行数

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.executemany(sql, params_list)
                    affected_rows = cursor.rowcount
                    conn.commit()
                    return affected_rows
        except Exception as e:
            logger.error(
                f"批量执行SQL失败: {sql}, 参数数量: {len(params_list)}, 错误: {e}"
            )
            raise DatabaseError(f"批量数据库操作失败: {str(e)}")

    @staticmethod
    def execute_script(script: str) -> None:
        """
        执行SQL脚本

        Args:
            script: SQL脚本内容

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 分割脚本为单独的语句
                    statements = [
                        stmt.strip() for stmt in script.split(";") if stmt.strip()
                    ]

                    for statement in statements:
                        cursor.execute(statement)

                    conn.commit()
                    logger.info("SQL脚本执行成功")
        except Exception as e:
            logger.error(f"执行SQL脚本失败: {e}")
            raise DatabaseError(f"SQL脚本执行失败: {str(e)}")

    @staticmethod
    def fetch_dict_one(
        sql: str, params: Optional[Tuple] = None
    ) -> Optional[Dict[str, Any]]:
        """
        查询单条记录并返回字典格式

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            Optional[Dict[str, Any]]: 查询结果字典，如果没有结果返回None

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    row = cursor.fetchone()
                    if row:
                        # 获取列名
                        columns = [desc[0] for desc in cursor.description]
                        return dict(zip(columns, row))
                    return None
        except Exception as e:
            logger.error(
                f"查询单条记录(字典格式)失败: {sql}, 参数: {params}, 错误: {e}"
            )
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def fetch_dict_all(
        sql: str, params: Optional[Tuple] = None
    ) -> List[Dict[str, Any]]:
        """
        查询多条记录并返回字典格式列表

        Args:
            sql: SQL语句
            params: 参数元组

        Returns:
            List[Dict[str, Any]]: 查询结果字典列表

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    rows = cursor.fetchall()
                    if rows:
                        # 获取列名
                        columns = [desc[0] for desc in cursor.description]
                        return [dict(zip(columns, row)) for row in rows]
                    return []
        except Exception as e:
            logger.error(
                f"查询多条记录(字典格式)失败: {sql}, 参数: {params}, 错误: {e}"
            )
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def count(
        table: str, where_clause: str = "", params: Optional[Tuple] = None
    ) -> int:
        """
        统计表中记录数量

        Args:
            table: 表名
            where_clause: WHERE子句（不包含WHERE关键字）
            params: 参数元组

        Returns:
            int: 记录数量

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            sql = f"SELECT COUNT(*) FROM {table}"
            if where_clause:
                sql += f" WHERE {where_clause}"

            result = DatabaseHelper.fetch_one(sql, params)
            return result[0] if result else 0
        except Exception as e:
            logger.error(f"统计记录数量失败: {table}, WHERE: {where_clause}, 错误: {e}")
            raise DatabaseError(f"统计记录数量失败: {str(e)}")

    @staticmethod
    def exists(table: str, where_clause: str, params: Optional[Tuple] = None) -> bool:
        """
        检查记录是否存在

        Args:
            table: 表名
            where_clause: WHERE子句（不包含WHERE关键字）
            params: 参数元组

        Returns:
            bool: 记录是否存在

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            sql = f"SELECT 1 FROM {table} WHERE {where_clause} LIMIT 1"
            result = DatabaseHelper.fetch_one(sql, params)
            return result is not None
        except Exception as e:
            logger.error(
                f"检查记录存在性失败: {table}, WHERE: {where_clause}, 错误: {e}"
            )
            raise DatabaseError(f"检查记录存在性失败: {str(e)}")

    @staticmethod
    def check_connection() -> bool:
        """
        检查数据库连接

        Returns:
            bool: 连接是否正常
        """
        try:
            DatabaseHelper.fetch_one("SELECT 1")
            return True
        except Exception:
            return False

    @staticmethod
    @contextmanager
    def transaction():
        """
        事务上下文管理器

        使用方式:
        with Database.transaction() as conn:
            Database.execute_with_conn(conn, "INSERT ...")
            Database.execute_with_conn(conn, "UPDATE ...")
        """
        conn = None
        try:
            conn = pg_manager.get_connection_direct()
            conn.autocommit = False
            yield conn
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"事务执行失败: {e}")
            raise DatabaseError(f"事务执行失败: {str(e)}")
        finally:
            if conn:
                conn.autocommit = True
                pg_manager.return_connection(conn)

    @staticmethod
    def execute_with_conn(conn, sql: str, params: Optional[Tuple] = None) -> int:
        """
        使用指定连接执行SQL语句

        Args:
            conn: 数据库连接
            sql: SQL语句
            params: 参数元组

        Returns:
            int: 受影响的行数

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库操作失败: {str(e)}")

    @staticmethod
    def fetch_one_with_conn(
        conn, sql: str, params: Optional[Tuple] = None
    ) -> Optional[Tuple]:
        """
        使用指定连接查询单条记录

        Args:
            conn: 数据库连接
            sql: SQL语句
            params: 参数元组

        Returns:
            Optional[Tuple]: 查询结果，如果没有结果返回None

        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchone()
        except Exception as e:
            logger.error(f"查询单条记录失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库查询失败: {str(e)}")


# 便捷函数
def execute(sql: str, params: Optional[Tuple] = None) -> int:
    """执行SQL语句的便捷函数"""
    return DatabaseHelper.execute(sql, params)


def fetch_one(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
    """查询单条记录的便捷函数"""
    return DatabaseHelper.fetch_one(sql, params)


def fetch_all(sql: str, params: Optional[Tuple] = None) -> List[Tuple]:
    """查询多条记录的便捷函数"""
    return DatabaseHelper.fetch_all(sql, params)


def execute_returning(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
    """执行SQL并返回结果的便捷函数"""
    return DatabaseHelper.execute_returning(sql, params)


def execute_many(sql: str, params_list: List[Tuple]) -> int:
    """批量执行SQL语句的便捷函数"""
    return DatabaseHelper.execute_many(sql, params_list)


def fetch_dict_one(
    sql: str, params: Optional[Tuple] = None
) -> Optional[Dict[str, Any]]:
    """查询单条记录并返回字典格式的便捷函数"""
    return DatabaseHelper.fetch_dict_one(sql, params)


def fetch_dict_all(sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
    """查询多条记录并返回字典格式列表的便捷函数"""
    return DatabaseHelper.fetch_dict_all(sql, params)


def count(table: str, where_clause: str = "", params: Optional[Tuple] = None) -> int:
    """统计表中记录数量的便捷函数"""
    return DatabaseHelper.count(table, where_clause, params)


def exists(table: str, where_clause: str, params: Optional[Tuple] = None) -> bool:
    """检查记录是否存在的便捷函数"""
    return DatabaseHelper.exists(table, where_clause, params)


def execute_script(script: str) -> None:
    """执行SQL脚本的便捷函数"""
    return DatabaseHelper.execute_script(script)


def check_connection() -> bool:
    """检查数据库连接的便捷函数"""
    return DatabaseHelper.check_connection()


# 事务管理便捷函数
def transaction():
    """事务上下文管理器的便捷函数"""
    return DatabaseHelper.transaction()
