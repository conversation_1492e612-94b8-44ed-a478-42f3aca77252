"""
统一数据库操作封装
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager
from app.config.postgresql import pg_manager

logger = logging.getLogger(__name__)


class DatabaseError(Exception):
    """数据库操作异常"""
    pass


class Database:
    """数据库操作封装类"""

    @staticmethod
    def execute(sql: str, params: Optional[Tuple] = None) -> int:
        """
        执行SQL语句（INSERT, UPDATE, DELETE）
        
        Args:
            sql: SQL语句
            params: 参数元组
            
        Returns:
            int: 受影响的行数
            
        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    affected_rows = cursor.rowcount
                    conn.commit()
                    return affected_rows
        except Exception as e:
            logger.error(f"执行SQL失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库操作失败: {str(e)}")

    @staticmethod
    def fetch_one(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
        """
        查询单条记录
        
        Args:
            sql: SQL语句
            params: 参数元组
            
        Returns:
            Optional[Tuple]: 查询结果，如果没有结果返回None
            
        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    return cursor.fetchone()
        except Exception as e:
            logger.error(f"查询单条记录失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def fetch_all(sql: str, params: Optional[Tuple] = None) -> List[Tuple]:
        """
        查询多条记录
        
        Args:
            sql: SQL语句
            params: 参数元组
            
        Returns:
            List[Tuple]: 查询结果列表
            
        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询多条记录失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库查询失败: {str(e)}")

    @staticmethod
    def execute_returning(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
        """
        执行SQL语句并返回结果（适用于INSERT ... RETURNING）
        
        Args:
            sql: SQL语句
            params: 参数元组
            
        Returns:
            Optional[Tuple]: 返回的结果
            
        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    result = cursor.fetchone()
                    conn.commit()
                    return result
        except Exception as e:
            logger.error(f"执行SQL并返回结果失败: {sql}, 参数: {params}, 错误: {e}")
            raise DatabaseError(f"数据库操作失败: {str(e)}")

    @staticmethod
    def execute_script(script: str) -> None:
        """
        执行SQL脚本
        
        Args:
            script: SQL脚本内容
            
        Raises:
            DatabaseError: 数据库操作失败
        """
        try:
            with pg_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 分割脚本为单独的语句
                    statements = [stmt.strip() for stmt in script.split(';') if stmt.strip()]
                    
                    for statement in statements:
                        cursor.execute(statement)
                    
                    conn.commit()
                    logger.info("SQL脚本执行成功")
        except Exception as e:
            logger.error(f"执行SQL脚本失败: {e}")
            raise DatabaseError(f"SQL脚本执行失败: {str(e)}")

    @staticmethod
    def check_connection() -> bool:
        """
        检查数据库连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            Database.fetch_one("SELECT 1")
            return True
        except Exception:
            return False

    @staticmethod
    @contextmanager
    def transaction():
        """
        事务上下文管理器
        
        使用方式:
        with Database.transaction():
            Database.execute("INSERT ...")
            Database.execute("UPDATE ...")
        """
        conn = None
        try:
            conn = pg_manager.get_connection_direct()
            conn.autocommit = False
            yield conn
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"事务执行失败: {e}")
            raise DatabaseError(f"事务执行失败: {str(e)}")
        finally:
            if conn:
                conn.autocommit = True
                pg_manager.return_connection(conn)


# 便捷函数
def execute(sql: str, params: Optional[Tuple] = None) -> int:
    """执行SQL语句的便捷函数"""
    return Database.execute(sql, params)


def fetch_one(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
    """查询单条记录的便捷函数"""
    return Database.fetch_one(sql, params)


def fetch_all(sql: str, params: Optional[Tuple] = None) -> List[Tuple]:
    """查询多条记录的便捷函数"""
    return Database.fetch_all(sql, params)


def execute_returning(sql: str, params: Optional[Tuple] = None) -> Optional[Tuple]:
    """执行SQL并返回结果的便捷函数"""
    return Database.execute_returning(sql, params)
