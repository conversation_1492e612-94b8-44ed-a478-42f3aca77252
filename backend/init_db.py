#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import os
import sys
import logging
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

from app.config.database import db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def init_database():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")

        # 检查数据库连接
        if not db_manager.check_connection():
            logger.error("数据库连接失败，请检查配置")
            return False

        logger.info("数据库连接正常")

        # 创建表结构
        db_manager.create_tables()
        logger.info("数据库表创建成功")

        logger.info("数据库初始化完成")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


if __name__ == "__main__":
    success = init_database()
    sys.exit(0 if success else 1)