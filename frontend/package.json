{"name": "translator-frontend", "version": "0.0.0", "private": true, "scripts": {"build": "vue-tsc && vite build", "dev": "vite", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@better-scroll/core": "^2.5.1", "@better-scroll/nested-scroll": "^2.5.1", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.1.1", "axios": "^1.11.0", "element-plus": "^2.4.4", "pinia": "^2.1.7", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.6.0", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.10.6", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.4.0", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}