/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_MAX_FILE_SIZE: string
  readonly VITE_ALLOWED_EXTENSIONS: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}