import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TranslationTask, ProgressUpdate, UploadStatus } from '@/types'

export const useTranslationStore = defineStore('translation', () => {
  // 状态
  const currentTask = ref<TranslationTask | null>(null)
  const uploadStatus = ref<UploadStatus>('idle')
  const progress = ref(0)
  const progressMessage = ref('')
  const error = ref<string | null>(null)
  const history = ref<TranslationTask[]>([])

  // 设置当前任务
  const setCurrentTask = (task: TranslationTask) => {
    currentTask.value = task
    progress.value = task.progress
    progressMessage.value = task.message
    uploadStatus.value = task.status === 'completed' ? 'completed' : 'processing'
  }

  // 更新进度
  const updateProgress = (update: ProgressUpdate) => {
    progress.value = update.progress
    progressMessage.value = update.message
    
    if (currentTask.value && currentTask.value.taskId === update.taskId) {
      currentTask.value.progress = update.progress
      currentTask.value.message = update.message
      
      if (update.status) {
        currentTask.value.status = update.status as any
        uploadStatus.value = update.status as UploadStatus
      }
    }
  }

  // 设置上传状态
  const setUploadStatus = (status: UploadStatus) => {
    uploadStatus.value = status
  }

  // 设置错误
  const setError = (errorMessage: string) => {
    error.value = errorMessage
    uploadStatus.value = 'error'
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  // 重置状态
  const reset = () => {
    currentTask.value = null
    uploadStatus.value = 'idle'
    progress.value = 0
    progressMessage.value = ''
    error.value = null
  }

  // 设置历史记录
  const setHistory = (tasks: TranslationTask[]) => {
    history.value = tasks
  }

  // 添加到历史记录
  const addToHistory = (task: TranslationTask) => {
    const existingIndex = history.value.findIndex(t => t.taskId === task.taskId)
    if (existingIndex >= 0) {
      history.value[existingIndex] = task
    } else {
      history.value.unshift(task)
    }
  }

  // 从历史记录中移除
  const removeFromHistory = (taskId: string) => {
    const index = history.value.findIndex(t => t.taskId === taskId)
    if (index >= 0) {
      history.value.splice(index, 1)
    }
  }

  // Getters
  const isProcessing = () => uploadStatus.value === 'processing' || uploadStatus.value === 'uploading'
  const isCompleted = () => uploadStatus.value === 'completed'
  const hasError = () => uploadStatus.value === 'error' || error.value !== null

  return {
    // 状态
    currentTask,
    uploadStatus,
    progress,
    progressMessage,
    error,
    history,
    
    // 动作
    setCurrentTask,
    updateProgress,
    setUploadStatus,
    setError,
    clearError,
    reset,
    setHistory,
    addToHistory,
    removeFromHistory,
    
    // Getters
    isProcessing,
    isCompleted,
    hasError,
  }
})