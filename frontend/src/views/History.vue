<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部 -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <router-link to="/" class="mr-4">
              <el-icon :size="24" class="text-gray-400 hover:text-gray-600">
                <ArrowLeft />
              </el-icon>
            </router-link>
            <h1 class="text-3xl font-bold text-gray-900">翻译历史</h1>
          </div>
          <div class="flex items-center space-x-4">
            <el-button @click="refreshHistory" :loading="loading">
              <el-icon class="mr-1">
                <Refresh />
              </el-icon>
              刷新
            </el-button>
            <router-link to="/">
              <el-button type="primary">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>
                新翻译
              </el-button>
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 筛选和搜索 -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex items-center space-x-4">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
              <el-option label="全部" value="" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </div>
          <div class="flex items-center space-x-4">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名..."
              clearable
              class="w-64"
            >
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </div>

      <!-- 历史记录列表 -->
      <div v-if="loading && history.length === 0" class="text-center py-12">
        <el-icon :size="48" class="text-gray-400 mb-4 animate-spin">
          <Loading />
        </el-icon>
        <p class="text-gray-500">加载中...</p>
      </div>

      <div v-else-if="filteredHistory.length === 0" class="text-center py-12">
        <el-icon :size="48" class="text-gray-400 mb-4">
          <DocumentCopy />
        </el-icon>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无翻译记录</h3>
        <p class="text-gray-500 mb-6">
          {{ history.length === 0 ? '还没有翻译过文件' : '没有找到符合条件的记录' }}
        </p>
        <router-link to="/">
          <el-button type="primary">
            开始翻译
          </el-button>
        </router-link>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="task in filteredHistory"
          :key="task.taskId"
          class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start flex-1">
              <!-- 文件图标 -->
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                <el-icon :size="24" class="text-blue-600">
                  <Document />
                </el-icon>
              </div>

              <!-- 文件信息 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-semibold text-gray-900 truncate">
                    {{ task.originalFilename }}
                  </h3>
                  <el-tag
                    :type="getStatusTagType(task.status)"
                    size="small"
                  >
                    {{ getStatusText(task.status) }}
                  </el-tag>
                </div>

                <p v-if="task.description" class="text-gray-600 mb-2 line-clamp-2">
                  {{ task.description }}
                </p>

                <div class="flex items-center text-sm text-gray-500 space-x-4">
                  <span class="flex items-center">
                    <el-icon :size="14" class="mr-1">
                      <Timer />
                    </el-icon>
                    {{ formatTime(task.createdTime) }}
                  </span>
                  <span v-if="task.status === 'processing'" class="flex items-center">
                    <el-icon :size="14" class="mr-1">
                      <Loading />
                    </el-icon>
                    {{ task.progress }}% - {{ task.message }}
                  </span>
                </div>

                <!-- 进度条（处理中时显示） -->
                <div v-if="task.status === 'processing'" class="mt-3">
                  <el-progress
                    :percentage="task.progress"
                    :stroke-width="6"
                    :show-text="false"
                  />
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center space-x-2 ml-4">
              <el-button
                v-if="task.status === 'completed'"
                type="primary"
                size="small"
                @click="downloadFile(task)"
              >
                <el-icon class="mr-1">
                  <Download />
                </el-icon>
                下载
              </el-button>
              <el-button
                v-if="task.status === 'processing'"
                type="info"
                size="small"
                @click="viewProgress(task)"
              >
                <el-icon class="mr-1">
                  <View />
                </el-icon>
                查看
              </el-button>
              <el-dropdown @command="handleTaskAction">
                <el-button size="small" type="info" plain>
                  <el-icon>
                    <MoreFilled />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'retry', task }">
                      重新翻译
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'delete', task }" divided>
                      删除记录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredHistory.length > 0" class="flex justify-center mt-8">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="filteredHistory.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ElButton,
  ElIcon,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElInput,
  ElTag,
  ElProgress,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElPagination,
  ElMessage,
  ElMessageBox
} from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Plus,
  Search,
  Loading,
  DocumentCopy,
  Document,
  Timer,
  Download,
  View,
  MoreFilled
} from '@element-plus/icons-vue'

import { useTranslationStore } from '@/stores/translation'
import { translateApiService } from '@/api/translate'
import type { TranslationTask } from '@/types'

// Router
const router = useRouter()

// Store
const store = useTranslationStore()

// 响应式数据
const loading = ref(false)
const history = ref<TranslationTask[]>([])
const statusFilter = ref('')
const dateRange = ref<[string, string] | null>(null)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const filteredHistory = computed(() => {
  let filtered = [...history.value]

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value)
  }

  // 日期筛选
  if (dateRange.value) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(task => {
      const taskDate = new Date(task.createdTime).toISOString().split('T')[0]
      return taskDate >= startDate && taskDate <= endDate
    })
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(task =>
      task.originalFilename.toLowerCase().includes(keyword) ||
      (task.description && task.description.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

// 获取历史记录
const fetchHistory = async () => {
  try {
    loading.value = true
    const response = await translateApiService.getHistory()
    history.value = response.history
    store.setHistory(response.history)
  } catch (error) {
    console.error('Failed to fetch history:', error)
    ElMessage.error('获取历史记录失败')
  } finally {
    loading.value = false
  }
}

// 刷新历史记录
const refreshHistory = () => {
  fetchHistory()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'processing':
      return 'warning'
    case 'failed':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'processing':
      return '处理中'
    case 'failed':
      return '失败'
    default:
      return '未知'
  }
}

// 格式化时间
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return '刚刚'
  if (diffInHours < 24) return `${diffInHours}小时前`
  if (diffInHours < 24 * 7) return `${Math.floor(diffInHours / 24)}天前`

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 下载文件
const downloadFile = async (task: TranslationTask) => {
  try {
    const blob = await translateApiService.downloadFile(task.taskId)
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `translated_${task.originalFilename}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('Download failed:', error)
    ElMessage.error('下载失败')
  }
}

// 查看进度
const viewProgress = (task: TranslationTask) => {
  store.setCurrentTask(task)
  router.push('/')
}

// 处理任务操作
const handleTaskAction = async ({ action, task }: { action: string; task: TranslationTask }) => {
  switch (action) {
    case 'retry':
      // TODO: 实现重新翻译功能
      ElMessage.info('重新翻译功能开发中...')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          '确定要删除这个翻译记录吗？此操作不可恢复。',
          '确认删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        await translateApiService.deleteTask(task.taskId)
        history.value = history.value.filter(t => t.taskId !== task.taskId)
        store.removeFromHistory(task.taskId)
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Delete failed:', error)
          ElMessage.error('删除失败')
        }
      }
      break
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生命周期
onMounted(() => {
  fetchHistory()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>