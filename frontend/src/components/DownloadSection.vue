<template>
  <div class="download-section">
    <div class="text-center mb-6">
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <el-icon :size="32" class="text-green-500">
          <CircleCheckFilled />
        </el-icon>
      </div>
      <h3 class="text-2xl font-bold text-gray-900 mb-2">翻译完成！</h3>
      <p class="text-gray-600">您的文件已成功翻译，可以下载了</p>
    </div>

    <!-- 文件信息 -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <div class="flex items-start justify-between">
        <div class="flex items-start">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <el-icon :size="24" class="text-blue-600">
              <Document />
            </el-icon>
          </div>
          <div class="flex-1">
            <h4 class="font-semibold text-gray-900 mb-1">
              {{ task.originalFilename }}
            </h4>
            <p v-if="task.description" class="text-sm text-gray-600 mb-2">
              {{ task.description }}
            </p>
            <div class="flex items-center text-sm text-gray-500 space-x-4">
              <span class="flex items-center">
                <el-icon :size="14" class="mr-1">
                  <Timer />
                </el-icon>
                {{ formatTime(task.createdTime) }}
              </span>
              <span class="flex items-center">
                <el-icon :size="14" class="mr-1">
                  <Check />
                </el-icon>
                翻译完成
              </span>
            </div>
          </div>
        </div>
        <div class="flex space-x-2">
          <el-button
            type="primary"
            :loading="downloading"
            @click="handleDownload"
          >
            <el-icon class="mr-1">
              <Download />
            </el-icon>
            下载文件
          </el-button>
          <el-button @click="handlePreview" v-if="false">
            <el-icon class="mr-1">
              <View />
            </el-icon>
            预览
          </el-button>
        </div>
      </div>
    </div>

    <!-- 翻译统计信息 -->
    <div v-if="translationStats" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-blue-600 mb-1">
          {{ translationStats.totalKeys }}
        </div>
        <div class="text-sm text-gray-600">翻译条目</div>
      </div>
      <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-green-600 mb-1">
          {{ translationStats.languages }}
        </div>
        <div class="text-sm text-gray-600">支持语言</div>
      </div>
      <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-purple-600 mb-1">
          {{ translationStats.duration }}
        </div>
        <div class="text-sm text-gray-600">处理时间</div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-center space-x-4">
      <el-button size="large" @click="handleNewUpload">
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        翻译新文件
      </el-button>
      <el-button size="large" type="info" plain @click="handleViewHistory">
        <el-icon class="mr-1">
          <Clock />
        </el-icon>
        查看历史
      </el-button>
    </div>

    <!-- 提示信息 -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start">
        <el-icon :size="20" class="text-blue-500 mr-3 mt-0.5">
          <InfoFilled />
        </el-icon>
        <div class="text-sm">
          <p class="text-blue-900 font-medium mb-1">文件保存提醒</p>
          <p class="text-blue-700">
            翻译后的文件将在服务器保存 {{ fileRetentionDays }} 天，请及时下载保存到本地。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import {
  CircleCheckFilled,
  Document,
  Timer,
  Check,
  Download,
  View,
  Plus,
  Clock,
  InfoFilled
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import type { TranslationTask } from '@/types'

// Props
interface Props {
  task: TranslationTask
  fileRetentionDays?: number
}

const props = withDefaults(defineProps<Props>(), {
  fileRetentionDays: 2
})

// Emits
const emit = defineEmits<{
  download: [taskId: string]
  newUpload: []
}>()

// Router
const router = useRouter()

// 响应式数据
const downloading = ref(false)

// 模拟翻译统计数据
const translationStats = computed(() => {
  // 这里应该从API获取真实的统计数据
  return {
    totalKeys: 156,
    languages: 4,
    duration: '2分35秒'
  }
})

// 格式化时间
const formatTime = (timeString: string): string => {
  const date = new Date(timeString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 1) return '刚刚'
  if (diffInMinutes < 60) return `${diffInMinutes}分钟前`
  if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理下载
const handleDownload = async () => {
  try {
    downloading.value = true
    emit('download', props.task.taskId)
    ElMessage.success('开始下载文件')
  } catch (error) {
    console.error('Download failed:', error)
    ElMessage.error('下载失败，请重试')
  } finally {
    downloading.value = false
  }
}

// 处理预览
const handlePreview = () => {
  // TODO: 实现文件预览功能
  ElMessage.info('预览功能开发中...')
}

// 处理新上传
const handleNewUpload = () => {
  emit('newUpload')
}

// 查看历史记录
const handleViewHistory = () => {
  router.push('/history')
}
</script>

<style scoped>
.download-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bg-blue-50 {
  animation: fadeIn 0.8s ease-out 0.2s both;
}

.bg-green-50 {
  animation: fadeIn 0.8s ease-out 0.4s both;
}

.bg-purple-50 {
  animation: fadeIn 0.8s ease-out 0.6s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>