<template>
  <div
    ref="itemContainer"
    class="px-4 py-2 transition-all duration-200 group hover:bg-gray-50/50"
    :style="{ minHeight: unifiedHeight ? `${unifiedHeight}px` : 'auto' }"
  >
    <!-- Key显示和信息栏 -->
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-2">
        <div :class="[
          'w-1.5 h-1.5 rounded-full flex-shrink-0',
          !text ? 'bg-orange-400' : 'bg-emerald-500'
        ]"></div>
        <span class="text-xs font-medium text-gray-600 bg-gray-100/80 px-2 py-0.5 rounded-md border border-gray-200/50">
          {{ translationKey }}
        </span>
      </div>
      <div class="flex items-center space-x-3 text-xs text-gray-400">
        <span class="flex items-center space-x-1">
          <span>{{ text.length }}</span>
          <span>字符</span>
        </span>
        <span class="bg-gray-100 px-1.5 py-0.5 rounded text-gray-500 font-mono">
          #{{ index + 1 }}
        </span>
      </div>
    </div>

    <!-- 翻译内容 -->
    <div class="space-y-2">
      <!-- 只读模式 -->
      <div v-if="!isEditing" class="flex items-center group">
        <div
          class="flex-1 cursor-pointer py-2 px-3 rounded-lg min-h-[2rem] flex items-center"
          @click="startEdit"
        >
          <p :class="[
            'text-sm leading-relaxed',
            !text ? 'text-gray-400 italic' : 'text-gray-700'
          ]">
            {{ text || '点击编辑翻译内容...' }}
          </p>
        </div>
        <el-button
          size="small"
          type="text"
          @click="startEdit"
          class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-100 hover:text-gray-600"
        >
          <el-icon :size="14"><Edit /></el-icon>
        </el-button>
      </div>

      <!-- 编辑模式 -->
      <div v-else class="space-y-2">
        <el-input
          v-model="editText"
          type="textarea"
          :rows="2"
          placeholder="请输入翻译内容..."
          @keydown.enter.ctrl="saveEdit"
          @keydown.esc="cancelEdit"
          class="text-sm"
        />
        <div class="flex justify-end space-x-2">
          <el-button
            size="small"
            @click="cancelEdit"
            class="text-xs"
          >
            取消
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="saveEdit"
            class="text-xs"
          >
            保存
          </el-button>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end mt-2">
      <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <el-button
          size="small"
          type="text"
          @click="copyText"
          title="复制"
          class="hover:bg-gray-100 hover:text-gray-600 transition-all duration-200 p-1"
        >
          <el-icon :size="12"><CopyDocument /></el-icon>
        </el-button>
        <el-button
          size="small"
          type="text"
          @click="clearText"
          title="清空"
          class="hover:bg-gray-100 hover:text-gray-600 transition-all duration-200 p-1"
        >
          <el-icon :size="12"><Delete /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElButton, ElInput, ElIcon, ElMessage } from 'element-plus'
import { Edit, CopyDocument, Delete } from '@element-plus/icons-vue'

interface Props {
  translationKey: string
  text: string
  index: number
  unifiedHeight?: number
}

interface Emits {
  (e: 'update', key: string, text: string): void
  (e: 'height-measured', height: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditing = ref(false)
const editText = ref('')
const itemContainer = ref<HTMLElement>()
const currentHeight = ref(0)

// 开始编辑
const startEdit = () => {
  editText.value = props.text
  isEditing.value = true
}

// 保存编辑
const saveEdit = () => {
  emit('update', props.translationKey, editText.value)
  isEditing.value = false
  ElMessage.success('保存成功')
}

// 取消编辑
const cancelEdit = () => {
  editText.value = ''
  isEditing.value = false
}

// 复制文本
const copyText = async () => {
  try {
    await navigator.clipboard.writeText(props.text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 清空文本
const clearText = () => {
  emit('update', props.translationKey, '')
  ElMessage.success('已清空')
}

// 测量当前高度
const measureHeight = () => {
  if (itemContainer.value) {
    const height = itemContainer.value.offsetHeight
    if (height !== currentHeight.value) {
      currentHeight.value = height
      emit('height-measured', height)
    }
  }
}

// 设置高度
const setHeight = (height: number) => {
  if (itemContainer.value) {
    itemContainer.value.style.minHeight = `${height}px`
  }
}

// 监听内容变化，重新测量高度
watch([() => props.text, isEditing], () => {
  nextTick(() => {
    measureHeight()
  })
}, { immediate: false })

// 监听编辑状态变化后重新测量
watch(isEditing, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      measureHeight()
    })
  }
})

onMounted(() => {
  nextTick(() => {
    measureHeight()
  })
})

// 暴露方法给父组件
defineExpose({
  measureHeight,
  setHeight
})
</script>

<style scoped>
.transition-colors {
  transition: background-color 0.2s ease;
}
</style>