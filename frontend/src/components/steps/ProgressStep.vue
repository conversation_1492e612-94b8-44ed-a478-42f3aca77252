<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">翻译进行中</h2>
      <p class="text-lg text-gray-600">
        正在使用 AI 翻译您的内容，请稍候...
      </p>
    </div>

    <!-- 进度卡片 -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
      <!-- 进度头部 -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <font-awesome-icon
                :icon="status === 'completed' ? 'check' : 'spinner'"
                :class="[
                  'text-white text-xl',
                  status === 'processing' ? 'animate-spin' : ''
                ]"
              />
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">
                {{ getStatusText() }}
              </h3>
              <p class="text-gray-600">
                {{ getStatusDescription() }}
              </p>
            </div>
          </div>

          <div class="text-right">
            <div class="text-3xl font-bold text-blue-600">
              {{ Math.round(progress) }}%
            </div>
            <div class="text-sm text-gray-600">完成进度</div>
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="px-8 py-6">
        <div class="mb-4">
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>翻译进度</span>
            <span>{{ Math.round(progress) }}% / 100%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            <div
              :class="[
                'h-full transition-all duration-500 ease-out',
                status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
              ]"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>

        <!-- 进度详情 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-2xl font-bold text-gray-900 mb-1">
              {{ estimatedItems }}
            </div>
            <div class="text-sm text-gray-600">预计条目</div>
          </div>
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600 mb-1">
              {{ processedItems }}
            </div>
            <div class="text-sm text-gray-600">已处理</div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600 mb-1">
              {{ remainingTime }}
            </div>
            <div class="text-sm text-gray-600">预计剩余</div>
          </div>
        </div>
      </div>

      <!-- 实时日志 -->
      <div class="border-t border-gray-100">
        <div class="px-8 py-4 bg-gray-50">
          <h4 class="text-lg font-semibold text-gray-900 mb-4">翻译日志</h4>
          <div class="bg-gray-900 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
            <div
              v-for="(log, index) in logs"
              :key="index"
              :class="[
                'mb-1',
                log.type === 'success' ? 'text-green-400' :
                log.type === 'error' ? 'text-red-400' :
                log.type === 'warning' ? 'text-yellow-400' :
                'text-gray-300'
              ]"
            >
              <span class="text-gray-500">[{{ log.timestamp }}]</span>
              {{ log.message }}
            </div>
            <div
              v-if="status === 'processing'"
              class="text-blue-400 animate-pulse"
            >
              <span class="text-gray-500">[{{ getCurrentTime() }}]</span>
              正在翻译中...
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态指示器 -->
    <div class="mt-8 flex justify-center">
      <div class="flex items-center space-x-4">
        <div
          v-for="(step, index) in progressSteps"
          :key="step.name"
          class="flex items-center"
        >
          <div
            :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300',
              currentProgressStep >= index
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-500'
            ]"
          >
            <font-awesome-icon
              v-if="currentProgressStep > index"
              icon="check"
            />
            <span v-else>{{ index + 1 }}</span>
          </div>
          <span
            :class="[
              'ml-2 text-sm font-medium',
              currentProgressStep >= index ? 'text-blue-600' : 'text-gray-500'
            ]"
          >
            {{ step.name }}
          </span>
          <div
            v-if="index < progressSteps.length - 1"
            :class="[
              'w-8 h-0.5 mx-4 transition-all duration-300',
              currentProgressStep > index ? 'bg-blue-500' : 'bg-gray-300'
            ]"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps<{
  progress: number
  status: 'idle' | 'processing' | 'completed' | 'error'
}>()

const emit = defineEmits<{
  complete: []
}>()

// 响应式数据
const logs = ref([
  { timestamp: '10:30:15', message: '开始初始化翻译任务...', type: 'info' },
  { timestamp: '10:30:16', message: '连接翻译服务成功', type: 'success' },
  { timestamp: '10:30:17', message: '开始处理源语言内容...', type: 'info' }
])

const progressSteps = [
  { name: '初始化' },
  { name: '分析内容' },
  { name: '翻译处理' },
  { name: '生成结果' }
]

// 计算属性
const estimatedItems = computed(() => 150)
const processedItems = computed(() => Math.round(props.progress * 1.5))
const remainingTime = computed(() => {
  if (props.status === 'completed') return '已完成'
  const remaining = Math.max(0, 100 - props.progress)
  const minutes = Math.ceil(remaining / 10)
  return `${minutes} 分钟`
})

const currentProgressStep = computed(() => {
  if (props.progress < 25) return 0
  if (props.progress < 50) return 1
  if (props.progress < 90) return 2
  return 3
})

// 方法
const getStatusText = () => {
  switch (props.status) {
    case 'processing':
      return '正在翻译'
    case 'completed':
      return '翻译完成'
    case 'error':
      return '翻译失败'
    default:
      return '准备中'
  }
}

const getStatusDescription = () => {
  switch (props.status) {
    case 'processing':
      return 'AI 正在处理您的翻译请求'
    case 'completed':
      return '所有内容已成功翻译完成'
    case 'error':
      return '翻译过程中出现错误'
    default:
      return '正在准备翻译任务'
  }
}

const getCurrentTime = () => {
  return new Date().toLocaleTimeString()
}

const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
  logs.value.push({
    timestamp: getCurrentTime(),
    message,
    type
  })
}

// 监听进度变化
watch(() => props.progress, (newProgress, oldProgress) => {
  if (newProgress > oldProgress) {
    if (newProgress >= 25 && oldProgress < 25) {
      addLog('内容分析完成，开始翻译处理...', 'success')
    } else if (newProgress >= 50 && oldProgress < 50) {
      addLog('翻译处理进行中...', 'info')
    } else if (newProgress >= 90 && oldProgress < 90) {
      addLog('翻译即将完成，正在生成结果...', 'info')
    }
  }
})

// 监听状态变化
watch(() => props.status, (newStatus) => {
  if (newStatus === 'completed') {
    addLog('翻译任务完成！', 'success')
    setTimeout(() => {
      emit('complete')
    }, 2000)
  } else if (newStatus === 'error') {
    addLog('翻译过程中出现错误，请重试', 'error')
  }
})

// 组件挂载时添加初始日志
onMounted(() => {
  setTimeout(() => {
    addLog('开始分析文件内容...', 'info')
  }, 1000)
})
</script>