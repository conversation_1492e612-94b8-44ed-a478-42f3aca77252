<template>
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">预览文件内容</h2>
      <p class="text-lg text-gray-600">
        确认文件内容无误后，点击下一步进行翻译配置
      </p>
    </div>

    <!-- 文件信息卡片 -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8"
    >
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-100"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div
              class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center"
            >
              <font-awesome-icon icon="file-excel" class="text-white text-xl" />
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">
                {{ excelData?.metadata.filename }}
              </h3>
              <p class="text-gray-600">
                工作表: {{ excelData?.metadata.sheet_name }}
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-6">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">
                {{ excelData?.languages.length }}
              </div>
              <div class="text-sm text-gray-600">语言</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">
                {{ excelData?.metadata.total_keys }}
              </div>
              <div class="text-sm text-gray-600">条目</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 语言列表 -->
      <div class="p-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-4">检测到的语言</h4>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div
            v-for="language in excelData?.languages"
            :key="language"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200"
          >
            <div
              class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
            >
              <font-awesome-icon icon="globe" class="text-blue-600 text-sm" />
            </div>
            <span class="font-medium text-gray-900"
              >{{ language }} ({{
                excelData?.language_names?.[language]
              }})</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 数据预览表格 -->
    <div
      class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
    >
      <div class="px-8 py-6 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <h4 class="text-lg font-semibold text-gray-900">数据预览</h4>
          <div class="flex items-center space-x-2 text-sm text-gray-600">
            <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
            <span>/</span>
            <span>共 {{ totalItems }} 条记录</span>
          </div>
        </div>
      </div>

      <!-- 固定表格容器 -->
      <div class="relative max-h-96 overflow-auto">
        <table class="w-full border-collapse">
          <!-- 固定表头 -->
          <thead class="bg-gray-50 sticky top-0 z-20">
            <tr>
              <!-- 固定的左上角单元格 -->
              <th
                class="sticky left-0 z-30 bg-gray-50 px-4 py-3 text-left text-sm font-semibold text-gray-900 border-r border-b border-gray-200 min-w-[100px] max-w-[200px]"
              >
                <div class="truncate">翻译键</div>
              </th>
              <!-- 语言列标题 -->
              <th
                v-for="language in excelData?.languages"
                :key="language"
                class="px-4 py-3 text-left text-sm font-semibold text-gray-900 border-r border-b border-gray-200 last:border-r-0 min-w-[100px] max-w-[200px] bg-gray-50"
              >
                <div
                  class="truncate"
                  :title="excelData?.language_names?.[language] || language"
                >
                  {{ language }} ({{ excelData?.language_names?.[language] }})
                </div>
              </th>
            </tr>
          </thead>
          <!-- 表格主体 -->
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="(translations, key, index) in limitedTranslations"
              :key="key"
              :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
            >
              <!-- 固定的第一列 -->
              <td
                class="sticky left-0 z-10 px-4 py-3 text-sm font-medium text-gray-900 border-r border-gray-200 min-w-[100px] max-w-[200px]"
                :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
              >
                <div class="truncate" :title="String(key)">{{ key }}</div>
              </td>
              <!-- 数据单元格 -->
              <td
                v-for="language in excelData?.languages"
                :key="language"
                class="px-4 py-3 text-sm text-gray-700 border-r border-gray-200 last:border-r-0 min-w-[100px] max-w-[200px]"
              >
                <div class="truncate" :title="translations[language] || '-'">
                  {{ translations[language] || '-' }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Element Plus 分页组件 -->
      <div
        v-if="totalPages > 1"
        class="px-8 py-4 bg-gray-50 border-t border-gray-200 flex justify-center"
      >
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[DEFAULT_PAGE_SIZE, 100, 150, 200]"
          :total="totalItems"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between mt-8">
      <button
        @click="$emit('back')"
        class="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-lg transition-colors duration-200"
      >
        <font-awesome-icon icon="arrow-left" class="mr-2" />
        重新上传
      </button>

      <button
        @click="$emit('confirm')"
        class="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
      >
        确认无误，下一步
        <font-awesome-icon icon="arrow-right" class="ml-2" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { ExcelData } from '@/types';

const props = defineProps<{
  excelData: ExcelData | null;
}>();

const emit = defineEmits<{
  confirm: [];
  back: [];
  restart: [];
}>();

// 响应式数据
const DEFAULT_PAGE_SIZE = 50;
const currentPage = ref(1);
const pageSize = ref(DEFAULT_PAGE_SIZE);

// 计算属性
const totalItems = computed(() => {
  if (!props.excelData) return 0;
  return Object.keys(props.excelData.translations).length;
});

const totalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value);
});

const limitedTranslations = computed(() => {
  if (!props.excelData) return {};

  const entries = Object.entries(props.excelData.translations);
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  const limited = entries.slice(startIndex, endIndex);
  return Object.fromEntries(limited);
});

// Element Plus 分页事件处理
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
};
</script>
