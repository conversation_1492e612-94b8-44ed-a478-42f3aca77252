<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-8">
      <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <font-awesome-icon icon="check-circle" class="text-green-600 text-3xl" />
      </div>
      <h2 class="text-3xl font-bold text-gray-900 mb-4">翻译完成！</h2>
      <p class="text-lg text-gray-600">
        您的文件已成功翻译，可以下载查看结果
      </p>
    </div>

    <!-- 结果统计 -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8">
      <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-8 py-6 border-b border-gray-100">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">翻译统计</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">
              {{ resultData?.statistics.translatedKeys }}
            </div>
            <div class="text-sm text-gray-600">已翻译条目</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">
              {{ resultData?.statistics.languages }}
            </div>
            <div class="text-sm text-gray-600">翻译语言</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-purple-600 mb-2">
              100%
            </div>
            <div class="text-sm text-gray-600">完成率</div>
          </div>
        </div>
      </div>

      <!-- 文件信息 -->
      <div class="p-8">
        <div class="flex items-center justify-between p-6 bg-gray-50 rounded-xl">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center">
              <font-awesome-icon icon="file-excel" class="text-white text-xl" />
            </div>
            <div>
              <h4 class="text-lg font-semibold text-gray-900">
                {{ resultData?.filename }}
              </h4>
              <p class="text-gray-600">
                翻译完成时间: {{ new Date().toLocaleString() }}
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <button
              @click="previewFile"
              class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium rounded-lg transition-colors duration-200"
            >
              <font-awesome-icon icon="eye" class="mr-2" />
              预览
            </button>
            <button
              @click="downloadFile"
              class="inline-flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
            >
              <font-awesome-icon icon="download" class="mr-2" />
              下载文件
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 质量报告 -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8">
      <div class="px-8 py-6 border-b border-gray-100">
        <h3 class="text-xl font-semibold text-gray-900">翻译质量报告</h3>
      </div>

      <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 语言质量 -->
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-4">各语言翻译质量</h4>
            <div class="space-y-3">
              <div
                v-for="language in sampleLanguages"
                :key="language.name"
                class="flex items-center justify-between"
              >
                <span class="text-gray-700 font-medium">{{ language.name }}</span>
                <div class="flex items-center space-x-2">
                  <div class="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      :class="[
                        'h-2 rounded-full',
                        language.quality >= 90 ? 'bg-green-500' :
                        language.quality >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                      ]"
                      :style="{ width: `${language.quality}%` }"
                    ></div>
                  </div>
                  <span class="text-sm font-medium text-gray-600">
                    {{ language.quality }}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 翻译建议 -->
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-4">优化建议</h4>
            <div class="space-y-3">
              <div class="flex items-start space-x-3">
                <font-awesome-icon icon="check-circle" class="text-green-500 mt-0.5" />
                <span class="text-gray-700">翻译准确性良好</span>
              </div>
              <div class="flex items-start space-x-3">
                <font-awesome-icon icon="check-circle" class="text-green-500 mt-0.5" />
                <span class="text-gray-700">语法结构正确</span>
              </div>
              <div class="flex items-start space-x-3">
                <font-awesome-icon icon="exclamation-circle" class="text-yellow-500 mt-0.5" />
                <span class="text-gray-700">部分专业术语需人工校对</span>
              </div>
              <div class="flex items-start space-x-3">
                <font-awesome-icon icon="info-circle" class="text-blue-500 mt-0.5" />
                <span class="text-gray-700">建议进行最终审核</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作选项 -->
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
      <h3 class="text-xl font-semibold text-gray-900 mb-6">接下来您可以</h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <button
          @click="downloadFile"
          class="flex flex-col items-center p-6 bg-green-50 hover:bg-green-100 rounded-xl border border-green-200 transition-colors duration-200"
        >
          <div class="w-12 h-12 bg-green-600 rounded-xl flex items-center justify-center mb-3">
            <font-awesome-icon icon="download" class="text-white text-xl" />
          </div>
          <h4 class="font-semibold text-gray-900 mb-2">下载结果</h4>
          <p class="text-sm text-gray-600 text-center">
            下载翻译完成的 Excel 文件
          </p>
        </button>

        <button
          @click="$emit('restart')"
          class="flex flex-col items-center p-6 bg-blue-50 hover:bg-blue-100 rounded-xl border border-blue-200 transition-colors duration-200"
        >
          <div class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-3">
            <font-awesome-icon icon="redo" class="text-white text-xl" />
          </div>
          <h4 class="font-semibold text-gray-900 mb-2">重新翻译</h4>
          <p class="text-sm text-gray-600 text-center">
            上传新文件或调整翻译设置
          </p>
        </button>

        <router-link
          to="/history"
          class="flex flex-col items-center p-6 bg-purple-50 hover:bg-purple-100 rounded-xl border border-purple-200 transition-colors duration-200"
        >
          <div class="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-3">
            <font-awesome-icon icon="history" class="text-white text-xl" />
          </div>
          <h4 class="font-semibold text-gray-900 mb-2">查看历史</h4>
          <p class="text-sm text-gray-600 text-center">
            查看之前的翻译记录
          </p>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  resultData: any
}>()

const emit = defineEmits<{
  restart: []
}>()

// 示例数据
const sampleLanguages = ref([
  { name: '英语 (English)', quality: 95 },
  { name: '日语 (Japanese)', quality: 92 },
  { name: '韩语 (Korean)', quality: 88 },
  { name: '法语 (French)', quality: 90 },
  { name: '德语 (German)', quality: 87 }
])

// 方法
const downloadFile = () => {
  // 模拟文件下载
  const link = document.createElement('a')
  link.href = props.resultData?.downloadUrl || '#'
  link.download = props.resultData?.filename || 'translated_file.xlsx'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const previewFile = () => {
  // 模拟文件预览
  alert('预览功能开发中...')
}
</script>