<template>
  <div class="progress-container">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">翻译进度</h3>
      <el-button 
        v-if="showCancel"
        size="small" 
        type="danger" 
        plain 
        @click="handleCancel"
      >
        取消
      </el-button>
    </div>

    <!-- 主进度条 -->
    <div class="mb-6">
      <el-progress
        :percentage="progress"
        :status="progressStatus"
        :stroke-width="12"
        :show-text="false"
      />
      <div class="flex justify-between items-center mt-2">
        <span class="text-sm text-gray-600">{{ message }}</span>
        <span class="text-sm font-medium text-gray-900">{{ progress }}%</span>
      </div>
    </div>

    <!-- 详细状态信息 -->
    <div class="bg-gray-50 rounded-lg p-4">
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <!-- 当前状态 -->
        <div class="flex items-center">
          <div 
            class="w-3 h-3 rounded-full mr-3"
            :class="statusIndicatorClass"
          ></div>
          <div>
            <div class="text-xs text-gray-500">当前状态</div>
            <div class="text-sm font-medium">{{ statusText }}</div>
          </div>
        </div>

        <!-- 已用时间 -->
        <div class="flex items-center">
          <el-icon :size="16" class="text-gray-400 mr-3">
            <Timer />
          </el-icon>
          <div>
            <div class="text-xs text-gray-500">已用时间</div>
            <div class="text-sm font-medium">{{ formatDuration(elapsedTime) }}</div>
          </div>
        </div>

        <!-- 预计剩余 -->
        <div class="flex items-center">
          <el-icon :size="16" class="text-gray-400 mr-3">
            <Clock />
          </el-icon>
          <div>
            <div class="text-xs text-gray-500">预计剩余</div>
            <div class="text-sm font-medium">{{ estimatedTimeRemaining }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阶段进度指示器 -->
    <div class="mt-6">
      <div class="flex items-center justify-between text-sm">
        <div
          v-for="(stage, index) in stages"
          :key="stage.key"
          class="flex items-center"
          :class="getStageClass(stage, index)"
        >
          <div
            class="w-8 h-8 rounded-full flex items-center justify-center mr-2"
            :class="getStageCircleClass(stage)"
          >
            <el-icon v-if="stage.completed" :size="16">
              <Check />
            </el-icon>
            <el-icon v-else-if="stage.active" :size="16" class="animate-spin">
              <Loading />
            </el-icon>
            <span v-else class="text-xs font-medium">{{ index + 1 }}</span>
          </div>
          <span class="font-medium">{{ stage.name }}</span>
          <div
            v-if="index < stages.length - 1"
            class="flex-1 h-0.5 mx-4"
            :class="stage.completed ? 'bg-green-300' : 'bg-gray-200'"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElProgress, ElButton, ElIcon } from 'element-plus'
import { Timer, Clock, Check, Loading } from '@element-plus/icons-vue'

// Props
interface Props {
  progress: number
  message: string
  showCancel?: boolean
  startTime?: Date
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
  message: '',
  showCancel: true,
  startTime: () => new Date()
})

// Emits
const emit = defineEmits<{
  cancel: []
}>()

// 响应式数据
const elapsedTime = ref(0)
const timer = ref<NodeJS.Timeout | null>(null)

// 翻译阶段定义
const stages = ref([
  { key: 'upload', name: '文件上传', completed: false, active: false },
  { key: 'parse', name: '解析文件', completed: false, active: false },
  { key: 'translate', name: 'AI翻译', completed: false, active: false },
  { key: 'generate', name: '生成文件', completed: false, active: false },
  { key: 'complete', name: '完成', completed: false, active: false }
])

// 计算属性
const progressStatus = computed(() => {
  if (props.progress === 100) return 'success'
  if (props.progress === 0) return undefined
  return undefined
})

const statusText = computed(() => {
  if (props.progress === 100) return '翻译完成'
  if (props.progress === 0) return '等待开始'
  return '翻译中'
})

const statusIndicatorClass = computed(() => {
  if (props.progress === 100) return 'bg-green-500 animate-pulse'
  if (props.progress === 0) return 'bg-gray-400'
  return 'bg-blue-500 animate-pulse'
})

const estimatedTimeRemaining = computed(() => {
  if (props.progress === 0 || props.progress === 100) return '--'
  
  const avgTimePerPercent = elapsedTime.value / props.progress
  const remainingPercent = 100 - props.progress
  const estimatedSeconds = Math.round(avgTimePerPercent * remainingPercent)
  
  return formatDuration(estimatedSeconds)
})

// 更新阶段状态
const updateStages = () => {
  const progress = props.progress
  
  // 重置所有阶段
  stages.value.forEach(stage => {
    stage.completed = false
    stage.active = false
  })
  
  if (progress >= 10) stages.value[0].completed = true  // 上传完成
  if (progress >= 20) stages.value[1].completed = true  // 解析完成
  if (progress >= 90) stages.value[2].completed = true  // 翻译完成
  if (progress >= 95) stages.value[3].completed = true  // 生成完成
  if (progress === 100) stages.value[4].completed = true  // 全部完成
  
  // 设置当前活跃阶段
  if (progress < 10) stages.value[0].active = true
  else if (progress < 20) stages.value[1].active = true
  else if (progress < 90) stages.value[2].active = true
  else if (progress < 95) stages.value[3].active = true
  else if (progress < 100) stages.value[4].active = true
}

// 获取阶段样式类
const getStageClass = (stage: any, index: number) => {
  if (stage.completed) return 'text-green-600'
  if (stage.active) return 'text-blue-600'
  return 'text-gray-400'
}

const getStageCircleClass = (stage: any) => {
  if (stage.completed) return 'bg-green-500 text-white'
  if (stage.active) return 'bg-blue-500 text-white'
  return 'bg-gray-200 text-gray-500'
}

// 格式化持续时间
const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}时${minutes}分`
}

// 取消处理
const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(() => {
  // 启动计时器
  timer.value = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - props.startTime.getTime()) / 1000)
  }, 1000)
  
  updateStages()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

// 监听进度变化
import { watch } from 'vue'
watch(() => props.progress, () => {
  updateStages()
})
</script>

<style scoped>
.progress-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>