<template>
  <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg border border-white/50 h-full flex flex-col min-w-[380px] hover:shadow-xl transition-all duration-300">
    <!-- 卡片头部 -->
    <div class="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50/80 to-blue-50/50 rounded-t-3xl">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm">
            <span class="text-white text-sm font-bold">{{ language.slice(0, 2).toUpperCase() }}</span>
          </div>
          <h3 class="text-lg font-semibold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            {{ language }}
          </h3>
        </div>
        <div class="flex items-center space-x-2">
          <el-badge :value="translations.length" class="item">
            <template #default>
              <span class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                {{ translations.length }}
              </span>
            </template>
          </el-badge>
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div
      ref="scrollWrapper"
      class="flex-1 overflow-y-auto custom-scrollbar"
      @scroll="handleNativeScroll"
    >
      <div ref="scrollContent" class="divide-y divide-gray-100">
        <TranslationItem
          v-for="(item, index) in translations"
          :key="item.key"
          :ref="(el) => setTranslationItemRef(el, item.key)"
          :translation-key="item.key"
          :text="item.text"
          :index="index"
          :unified-height="unifiedHeights?.[item.key]"
          @update="handleUpdate"
          @height-measured="(height) => handleHeightMeasured(item.key, height)"
        />

        <!-- 空状态 -->
        <div v-if="translations.length === 0" class="text-center py-12">
          <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <el-icon :size="24" class="text-gray-400">
              <Document />
            </el-icon>
          </div>
          <p class="text-gray-500 font-medium">暂无翻译内容</p>
          <p class="text-gray-400 text-sm mt-1">等待数据加载...</p>
        </div>
      </div>
    </div>

    <!-- 卡片底部统计 -->
    <div class="px-6 py-3 border-t border-gray-100 bg-gradient-to-r from-gray-50/80 to-blue-50/30 rounded-b-3xl">
      <div class="flex items-center justify-between text-sm">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span class="text-gray-600 font-medium">共 {{ translations.length }} 条翻译</span>
        </div>
        <div class="flex items-center space-x-4 text-xs">
          <span class="text-green-600 font-medium">
            {{ translations.filter(t => t.text).length }} 已完成
          </span>
          <span class="text-orange-500 font-medium">
            {{ translations.filter(t => !t.text).length }} 待翻译
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElBadge, ElIcon } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import TranslationItem from './TranslationItem.vue'

interface Translation {
  key: string
  text: string
}

interface Props {
  language: string
  translations: Translation[]
  unifiedHeights?: Record<string, number>
  scrollGroupId?: string
}

interface Emits {
  (e: 'update', key: string, text: string): void
  (e: 'scroll-sync', position: { x: number; y: number }, sourceId: string): void
  (e: 'height-update', key: string, height: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const scrollWrapper = ref<HTMLElement>()
const scrollContent = ref<HTMLElement>()
const translationItemRefs = ref<Record<string, InstanceType<typeof TranslationItem>>>({})
let isScrollSyncing = false
const cardId = `card-${props.language}-${Math.random().toString(36).substring(2, 11)}`

// 处理翻译更新
const handleUpdate = (key: string, text: string) => {
  emit('update', key, text)
}

// 处理原生滚动事件
const handleNativeScroll = (event: Event) => {
  if (!isScrollSyncing) {
    const target = event.target as HTMLElement
    const position = {
      x: target.scrollLeft,
      y: target.scrollTop
    }
    emit('scroll-sync', position, cardId)
  }
}

// 原生滚动同步
const syncNativeScrollPosition = (position: { x: number; y: number }, sourceId: string) => {
  if (scrollWrapper.value && sourceId !== cardId) {
    isScrollSyncing = true
    scrollWrapper.value.scrollTop = position.y
    scrollWrapper.value.scrollLeft = position.x
    nextTick(() => {
      isScrollSyncing = false
    })
  }
}

// 设置翻译项引用
const setTranslationItemRef = (el: any, key: string) => {
  if (el) {
    translationItemRefs.value[key] = el
  }
}

// 处理高度测量
const handleHeightMeasured = (key: string, height: number) => {
  emit('height-update', key, height)
}

// 更新单个项目高度
const updateItemHeight = (key: string, height: number) => {
  const itemRef = translationItemRefs.value[key]
  if (itemRef) {
    itemRef.setHeight?.(height)
  }
}

// 测量所有项目高度
const measureAllHeights = () => {
  Object.entries(translationItemRefs.value).forEach(([key, itemRef]) => {
    if (itemRef) {
      itemRef.measureHeight?.()
    }
  })
}

// 暴露方法给父组件
defineExpose({
  syncScrollPosition: syncNativeScrollPosition,
  updateItemHeight,
  measureAllHeights,
  cardId
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>