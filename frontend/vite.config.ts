import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 23876,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:32982',
        changeOrigin: true,
        secure: false,
      },
      '/ws': {
        target: 'ws://localhost:32982',
        ws: true,
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})