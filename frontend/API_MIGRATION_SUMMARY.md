# API迁移总结

## 概述
已成功将前端所有API调用从原生fetch替换为统一的request.helper.ts封装。

## 主要更改

### 1. 新增request.helper.ts
- 位置: `frontend/src/utils/request.helper.ts`
- 功能: 统一的axios封装，自动处理API响应
- 特性:
  - code为0时返回data字段
  - code不为0时抛出错误
  - 支持Blob类型响应（文件下载）
  - 统一错误处理

### 2. 类型定义更新
- 文件: `frontend/src/api/translate/types.ts`
- 新增枚举:
  - `ApiStatus`: API状态码枚举
  - `TranslationStatus`: 翻译状态枚举
  - `TranslationStyle`: 翻译风格枚举
  - `TranslationDomain`: 翻译领域枚举

### 3. API服务重构
- 文件: `frontend/src/api/translate/service.ts`
- 所有方法已替换为使用request.helper.ts
- 新增方法:
  - `downloadFile(taskId: string)`: 下载翻译结果文件
  - `deleteTask(taskId: string)`: 删除翻译任务

### 4. 组件更新

#### FileUploadStep.vue
- 替换直接fetch调用为translateApiService
- 导入translateApiService

#### Home.vue
- 替换apiService为translateApiService
- 使用枚举替代硬编码字符串
- 更新导入语句

#### History.vue
- 替换apiService为translateApiService
- 更新API调用方法

#### FileUpload.vue
- 替换apiService为translateApiService
- 更新API调用方法

### 5. 兼容性层更新
- 文件: `frontend/src/services/api.ts`
- 保持向后兼容性
- 新增downloadFile和deleteTask方法
- 添加类型注解

### 6. 依赖更新
- 新增axios依赖到package.json

## 使用方式

### 直接使用request helper
```typescript
import request from '@/utils/request.helper'

// GET请求
const data = await request.get<UserInfo>('/api/user')

// POST请求
const result = await request.post('/api/login', { username, password })
```

### 使用API服务
```typescript
import { translateApiService } from '@/api/translate'

// 上传文件
const result = await translateApiService.uploadFile(file)

// 获取历史记录
const history = await translateApiService.getHistory()
```

### 使用枚举
```typescript
import { TranslationStyle, TranslationDomain } from '@/api/translate'

const config = {
  style: TranslationStyle.FORMAL,
  domain: TranslationDomain.GENERAL,
  preserve_formatting: true
}
```

## 错误处理
所有API调用现在统一使用try-catch处理错误：

```typescript
try {
  const result = await translateApiService.uploadFile(file)
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('API调用失败:', error.message)
}
```

## 注意事项
1. 所有API调用现在会自动处理code字段，成功时直接返回data内容
2. 错误会自动抛出，需要使用try-catch捕获
3. 文件下载等特殊响应类型会自动识别并正确处理
4. 保持了向后兼容性，现有代码可以继续使用apiService