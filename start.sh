#!/bin/bash

# 启动脚本 - 同时启动前后端服务

echo "🚀 启动翻译工具服务..."

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口 $1 已被占用，请先关闭相关进程"
        return 1
    fi
    return 0
}

# 检查后端端口
if ! check_port 32982; then
    exit 1
fi

# 检查前端端口
if ! check_port 23876; then
    exit 1
fi

# 启动后端服务
echo "📦 启动后端服务 (端口: 32982)..."
cd backend
python run.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务
echo "🎨 启动前端服务 (端口: 23876)..."
cd ../frontend
pnpm dev &
FRONTEND_PID=$!

echo "✅ 服务启动完成!"
echo "📱 前端地址: http://localhost:23876"
echo "🔧 后端地址: http://localhost:32982"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait