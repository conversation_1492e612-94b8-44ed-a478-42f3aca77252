#!/usr/bin/env python3
"""
测试新的翻译JSON格式
"""

import json

# 测试数据 - 符合新的JSON格式
test_translation_request = {
    "config": {
        "overwriteExisting": True,
        "skipEmpty": False,
        "preserveFormatting": True,
        "batchSize": 10
    },
    "batches": [
        {
            "batchId": "batch_001",
            "items": [
                {
                    "key": "app.title",
                    "sourceText": "应用标题",
                    "sourceLanguage": "zh-CN",
                    "targetLanguages": ["en-US", "ja-JP"],
                    "context": "应用程序标题",
                    "existingTranslations": {
                        "en-US": "",
                        "ja-JP": None,
                        "ko-KR": "앱 제목"
                    }
                },
                {
                    "key": "app.description", 
                    "sourceText": "这是应用描述",
                    "sourceLanguage": "zh-CN",
                    "targetLanguages": ["en-US", "ja-JP", "ko-KR"],
                    "context": "应用程序描述",
                    "existingTranslations": {
                        "en-US": "",
                        "ja-JP": "",
                        "ko-KR": ""
                    }
                }
            ]
        }
    ]
}

def test_json_format():
    """测试JSON格式是否正确"""
    try:
        # 序列化为JSON字符串
        json_str = json.dumps(test_translation_request, ensure_ascii=False, indent=2)
        print("✅ JSON格式正确")
        print("📄 生成的JSON:")
        print(json_str)
        
        # 反序列化测试
        parsed = json.loads(json_str)
        print("\n✅ JSON解析成功")
        
        # 验证必要字段
        assert "config" in parsed
        assert "batches" in parsed
        assert len(parsed["batches"]) > 0
        assert "batchId" in parsed["batches"][0]
        assert "items" in parsed["batches"][0]
        
        print("✅ 所有必要字段验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试新的翻译JSON格式...")
    success = test_json_format()
    if success:
        print("\n🎉 所有测试通过！新的JSON格式可以正常使用。")
    else:
        print("\n💥 测试失败，请检查JSON格式。")
