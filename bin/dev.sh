#!/bin/bash

# 启动完整开发环境（前端 + 后端）
# Author: Assistant
# Created: 2025-07-25

set -e

echo "🚀 启动完整开发环境..."

# 检查依赖工具
check_dependencies() {
    if ! command -v pnpm &> /dev/null; then
        echo "❌ pnpm 未安装，请先安装 pnpm"
        exit 1
    fi

    if ! command -v uv &> /dev/null; then
        echo "❌ uv 未安装，请先安装 uv"
        exit 1
    fi
}

# 清理函数 - 当脚本退出时杀死所有子进程
cleanup() {
    echo ""
    echo "🛑 正在停止开发服务器..."
    kill $(jobs -p) 2>/dev/null || true
    wait
    echo "✅ 开发环境已停止"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 启动前端开发服务器
start_frontend() {
    echo "🎨 启动前端开发服务器..."
    cd frontend

    if [ ! -f "package.json" ]; then
        echo "❌ 前端项目未初始化，请先运行 ./bin/setup.sh"
        exit 1
    fi

    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        echo "📦 安装前端依赖..."
        pnpm install
    fi

    # 检查环境变量
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        cp .env.example .env
    fi

    echo "✅ 前端服务启动中... (http://localhost:23876)"
    pnpm dev &
    FRONTEND_PID=$!
    cd ..
}

# 启动后端开发服务器
start_backend() {
    echo "🐍 启动后端开发服务器..."
    cd backend

    if [ ! -f "pyproject.toml" ]; then
        echo "❌ 后端项目未初始化，请先运行 ./bin/setup.sh"
        exit 1
    fi

    # 检查环境变量
    # if [ ! -f ".env" ] && [ -f ".env.example" ]; then
    #     cp .env.example .env
    #     echo "⚠️  请编辑 backend/.env 文件，配置你的 API 密钥和数据库连接"
    # fi

    # 创建必要目录
    mkdir -p ../uploads ../downloads ../logs

    # 安装依赖
    if [ ! -d ".venv" ]; then
        echo "📦 创建 Python 虚拟环境..."
        uv venv
    fi

    echo "📦 启动后端虚拟环境..."
    source .venv/bin/activate

    echo "📦 安装后端依赖..."
    uv sync

    echo "✅ 后端服务启动中... (http://localhost:32982)"
    uv run python -m app.main &
    BACKEND_PID=$!
    cd ..
}

# 显示服务状态
show_status() {
    echo ""
    echo "🎉 开发环境启动完成！"
    echo ""
    echo "📋 服务地址:"
    echo "   前端: http://localhost:23876"
    echo "   后端: http://localhost:32982"
    echo ""
    echo "🔧 开发工具:"
    echo "   前端热重载: 已启用"
    echo "   后端调试模式: 已启用"
    echo ""
    echo "💡 提示: 按 Ctrl+C 停止所有服务"
    echo ""
}

# 等待服务启动
wait_for_services() {
    echo "⏳ 等待服务启动..."
    sleep 3

    # 检查前端服务
    if curl -s http://localhost:23876 > /dev/null 2>&1; then
        echo "✅ 前端服务运行正常"
    else
        echo "⚠️  前端服务可能还在启动中..."
    fi

    # 检查后端服务
    if curl -s http://localhost:32982 > /dev/null 2>&1; then
        echo "✅ 后端服务运行正常"
    else
        echo "⚠️  后端服务可能还在启动中..."
    fi
}

# 主执行流程
main() {
    echo "📍 当前目录: $(pwd)"

    check_dependencies

    # 并行启动前后端服务
    start_frontend
    start_backend

    # 等待服务启动并显示状态
    wait_for_services
    show_status

    # 等待用户中断
    wait
}

# 执行主函数
main "$@"