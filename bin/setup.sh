#!/bin/bash

# 在线翻译网站项目初始化脚本
# Author: Assistant
# Created: 2025-07-25

set -e

echo "🚀 开始初始化在线翻译网站项目..."

# 检查必要的工具
check_dependencies() {
    echo "📋 检查依赖工具..."
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo "❌ pnpm 未安装，请先安装 pnpm"
        echo "   npm install -g pnpm"
        exit 1
    fi
    
    # 检查 uv
    if ! command -v uv &> /dev/null; then
        echo "❌ uv 未安装，请先安装 uv"
        echo "   pip install uv"
        exit 1
    fi
    
    # 检查 PostgreSQL
    if ! command -v psql &> /dev/null; then
        echo "⚠️  PostgreSQL 未检测到，请确保已安装并启动"
    fi
    
    echo "✅ 依赖检查完成"
}

# 初始化前端项目
init_frontend() {
    echo "🎨 初始化前端项目..."
    
    if [ ! -d "frontend" ]; then
        echo "❌ frontend 目录不存在"
        exit 1
    fi
    
    cd frontend
    
    # 检查是否已经初始化
    if [ -f "package.json" ]; then
        echo "📦 前端项目已存在，跳过初始化"
        cd ..
        return
    fi
    
    # 使用 Vite 创建 Vue3 + TypeScript 项目
    pnpm create vue@latest . --typescript --router --pinia --eslint --prettier
    
    # 安装依赖
    pnpm install
    
    # 安装额外依赖
    pnpm add element-plus @element-plus/icons-vue
    pnpm add -D tailwindcss postcss autoprefixer @types/node
    
    cd ..
    echo "✅ 前端项目初始化完成"
}

# 初始化后端项目
init_backend() {
    echo "🐍 初始化后端项目..."
    
    if [ ! -d "backend" ]; then
        echo "❌ backend 目录不存在"
        exit 1
    fi
    
    cd backend
    
    # 检查是否已经初始化
    if [ -f "pyproject.toml" ]; then
        echo "📦 后端项目已存在，跳过初始化"
        cd ..
        return
    fi
    
    # 初始化 uv 项目
    uv init --app --python 3.11
    
    # 添加依赖
    uv add flask flask-socketio flask-cors
    uv add pandas openpyxl xlrd
    uv add langchain dashscope
    uv add psycopg2-binary sqlalchemy
    uv add python-dotenv
    uv add --dev pytest black flake8
    
    cd ..
    echo "✅ 后端项目初始化完成"
}

# 创建环境配置文件
create_env_files() {
    echo "📝 创建环境配置文件..."
    
    # 后端环境配置
    if [ ! -f "backend/.env.example" ]; then
        cat > backend/.env.example << EOF
# Flask配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/translator_db

# qwen百炼配置
QWEN_API_KEY=your-qwen-api-key-here

# 文件存储配置
UPLOAD_FOLDER=../uploads
DOWNLOAD_FOLDER=../downloads
MAX_FILE_SIZE=50MB

# 翻译配置
MAX_CONCURRENT_TRANSLATIONS=10
FILE_CLEANUP_HOURS=48
EOF
    fi
    
    # 前端环境配置
    if [ ! -f "frontend/.env.example" ]; then
        cat > frontend/.env.example << EOF
# API配置
VITE_API_BASE_URL=http://localhost:32982
VITE_WS_URL=ws://localhost:32982

# 上传配置
VITE_MAX_FILE_SIZE=50MB
VITE_ALLOWED_EXTENSIONS=.xlsx,.xls
EOF
    fi
    
    echo "✅ 环境配置文件创建完成"
}

# 创建 README
create_readme() {
    echo "📚 创建项目文档..."
    
    if [ ! -f "README.md" ]; then
        cat > README.md << EOF
# 在线翻译网站

基于 Vue3 + Flask 的 Excel 文件在线翻译平台

## 功能特性

- 🎯 大文件拖拽上传支持
- 📊 Excel 智能解析和翻译
- ⚡ 异步批量翻译处理
- 📈 实时翻译进度显示
- 💾 本地文件存储管理
- 🔄 支持多语言翻译

## 技术栈

### 前端
- Vue 3 + TypeScript
- Element Plus UI
- TailwindCSS
- Pinia 状态管理
- WebSocket 实时通信

### 后端
- Python + Flask
- Langchain + qwen百炼
- PostgreSQL 数据库
- asyncio 异步处理
- Flask-SocketIO

## 快速开始

### 1. 项目初始化
\`\`\`bash
# 运行初始化脚本
chmod +x bin/setup.sh
./bin/setup.sh
\`\`\`

### 2. 环境配置
\`\`\`bash
# 复制并配置环境变量
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
# 编辑 .env 文件，填入你的配置
\`\`\`

### 3. 启动开发环境
\`\`\`bash
# 启动开发环境（前后端同时）
chmod +x bin/dev.sh
./bin/dev.sh

# 或分别启动
chmod +x bin/start-frontend.sh bin/start-backend.sh
./bin/start-frontend.sh  # 前端: http://localhost:23876
./bin/start-backend.sh   # 后端: http://localhost:32982
\`\`\`

## 项目结构

\`\`\`
com-petkit-translator/
├── bin/                    # 项目管理脚本
├── frontend/               # Vue3 前端项目
├── backend/                # Flask 后端项目
├── uploads/                # 临时上传文件
├── downloads/              # 翻译结果文件  
└── logs/                   # 日志文件
\`\`\`

## 开发指南

### Excel 文件格式要求

| Key | zh-CN | en-US | zh-TW | de-de |
|-----|-------|-------|-------|-------|
| a.b.c | 中文翻译 | English Context | | |
| a1.b1.c1 | 中文翻译1 | English Context2 | | |

- 第一行为语言标识
- 第一列为 Key 唯一标识
- 空值单元格将被自动翻译

### API 接口

- \`POST /api/upload\` - 文件上传
- \`GET /api/download/:taskId\` - 文件下载  
- \`GET /api/progress/:taskId\` - 获取进度
- \`WebSocket /ws/progress/:taskId\` - 实时进度推送

## 许可证

MIT License
EOF
    fi
    
    echo "✅ 项目文档创建完成"
}

# 主执行流程
main() {
    echo "📍 当前目录: $(pwd)"
    
    check_dependencies
    create_env_files
    create_readme
    init_frontend
    init_backend
    
    echo ""
    echo "🎉 项目初始化完成！"
    echo ""
    echo "📋 下一步操作："
    echo "   1. 配置环境变量: cp backend/.env.example backend/.env"
    echo "   2. 启动开发环境: ./bin/dev.sh"
    echo "   3. 访问前端页面: http://localhost:23876"
    echo ""
}

# 执行主函数
main "$@"