#!/bin/bash

# 启动前端开发服务器
# Author: Assistant
# Created: 2025-07-25

set -e

echo "🎨 启动前端开发服务器..."

# 检查前端目录
if [ ! -d "frontend" ]; then
    echo "❌ frontend 目录不存在，请先运行 ./bin/setup.sh"
    exit 1
fi

cd frontend

# 检查是否已初始化
if [ ! -f "package.json" ]; then
    echo "❌ 前端项目未初始化，请先运行 ./bin/setup.sh"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "📝 复制环境变量配置文件..."
        cp .env.example .env
    else
        echo "⚠️  未找到环境变量配置文件"
    fi
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    pnpm install
fi

echo "🚀 启动前端开发服务器 (http://localhost:23876)..."
pnpm dev