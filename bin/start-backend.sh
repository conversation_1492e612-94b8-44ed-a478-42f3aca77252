#!/bin/bash

# 启动后端开发服务器
# Author: Assistant
# Created: 2025-07-25

set -e

echo "🐍 启动后端开发服务器..."

# 检查后端目录
if [ ! -d "backend" ]; then
    echo "❌ backend 目录不存在，请先运行 ./bin/setup.sh"
    exit 1
fi

cd backend

# 检查是否已初始化
if [ ! -f "pyproject.toml" ]; then
    echo "❌ 后端项目未初始化，请先运行 ./bin/setup.sh"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo "📝 复制环境变量配置文件..."
        cp .env.example .env
        echo "⚠️  请编辑 backend/.env 文件，配置你的 API 密钥和数据库连接"
    else
        echo "⚠️  未找到环境变量配置文件"
    fi
fi

# 创建上传和下载目录
mkdir -p ../uploads ../downloads ../logs

# 检查 Python 虚拟环境
if [ ! -d ".venv" ]; then
    echo "📦 创建 Python 虚拟环境..."
    uv venv
fi

# 激活虚拟环境并安装依赖
echo "📦 安装后端依赖..."
uv sync

# 检查数据库连接
echo "🔍 检查数据库连接..."
if ! uv run python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('数据库 URL:', os.getenv('DATABASE_URL', 'None'))
" 2>/dev/null; then
    echo "⚠️  请确保已正确配置数据库连接"
fi

echo "🚀 启动后端开发服务器 (http://localhost:32982)..."
uv run python -m app.main