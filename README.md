# 翻译工具

基于Flask + Vue3的Excel翻译工具，支持多语言翻译管理和实时编辑。

## 功能特性

- 📁 **Excel文件上传**: 支持.xlsx和.xls格式文件
- 🌍 **多语言支持**: 自动识别Excel中的多语言列
- 📝 **实时编辑**: 支持在线编辑翻译内容
- 🔄 **滚动同步**: 多语言卡片滚动联动
- 💾 **数据持久化**: 编辑内容实时保存
- 🎨 **现代UI**: 基于Element Plus的美观界面

## 项目结构

```
├── backend/                 # Flask后端
│   ├── app/
│   │   ├── api/            # API接口层
│   │   ├── services/       # 业务服务层
│   │   ├── models/         # 数据模型层
│   │   └── config/         # 配置层
│   ├── .env                # 环境配置
│   └── run.py              # 启动脚本
├── frontend/               # Vue3前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── services/       # API服务
│   │   └── types/          # 类型定义
│   └── package.json
└── start.sh                # 一键启动脚本
```

## 快速开始

### 环境要求

- Python 3.11+
- Node.js 18+
- pnpm

### 安装依赖

```bash
# 后端依赖
cd backend
uv sync

# 前端依赖
cd ../frontend
pnpm install
```

### 启动服务

```bash
# 一键启动前后端服务
./start.sh
```

或者分别启动：

```bash
# 启动后端 (端口: 32982)
cd backend
python run.py

# 启动前端 (端口: 23876)
cd frontend
pnpm dev
```

### 访问应用

- 前端界面: http://localhost:23876
- 后端API: http://localhost:32982

## 使用说明

### 1. 上传Excel文件

Excel文件格式要求：
- 第一行为标题行
- 第一列为翻译key
- 其他列为不同语言的翻译内容

示例格式：
| Key | 中文 | English | 日本語 |
|-----|------|---------|--------|
| hello | 你好 | Hello | こんにちは |
| goodbye | 再见 | Goodbye | さようなら |

### 2. 查看和编辑翻译

- 上传成功后，系统会自动解析Excel并显示多语言卡片
- 点击任意翻译内容可进行在线编辑
- 所有卡片支持滚动联动，方便对比不同语言

### 3. 功能按钮

- **加载示例数据**: 快速加载测试数据
- **上传Excel文件**: 上传自己的翻译文件
- **开始翻译**: 批量翻译功能（开发中）

## API接口

### 文本翻译
```http
POST /api/translate
Content-Type: application/json

{
    "text": "Hello world",
    "source_lang": "en",
    "target_lang": "zh"
}
```

### Excel文件上传
```http
POST /api/translate
Content-Type: multipart/form-data

file: [Excel文件]
```

### 获取示例数据
```http
GET /api/translate/sample
```



## 技术栈

### 后端
- **Flask**: Web框架
- **Pydantic**: 数据验证
- **OpenPyXL**: Excel文件处理
- **Flask-CORS**: 跨域支持

### 前端
- **Vue 3**: 前端框架
- **TypeScript**: 类型支持
- **Element Plus**: UI组件库
- **Tailwind CSS**: 样式框架
- **Pinia**: 状态管理
- **VueUse**: 工具库

## 开发说明

### 后端开发

```bash
cd backend

# 安装新依赖
uv add package_name

# 运行测试
python test_api.py

# 代码格式化
black app/
```

### 前端开发

```bash
cd frontend

# 安装新依赖
pnpm add package_name

# 类型检查
pnpm run type-check

# 代码格式化
pnpm run format
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。